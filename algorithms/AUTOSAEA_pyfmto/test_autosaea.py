"""
Test Script for AUTOSAEA Algorithm

This script tests the AUTOSAEA algorithm implementation in the pyfmto framework.
It includes tests for individual components, single-client optimization,
and multi-client federated optimization.
"""

import numpy as np
import multiprocessing as mp
import time
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../src')))

from pyfmto.problems import SingleTaskProblem
from algorithms.AUTOSAEA_pyfmto import AutoSAEAClient, AutoSAEAServer
from algorithms.AUTOSAEA_pyfmto.autosaea_utils import (
    GPModel, RBFModel, PRSModel, KNNModel,
    LCBCriterion, EICriterion, PrescreeningCriterion,
    TwoLevelMAB, DEOperator, latin_hypercube_sampling
)


class TestProblem(SingleTaskProblem):
    """Simple test problem for optimization"""
    def __init__(self, client_id, dimension=10, max_fe=200):
        # Create different shifts for different clients to simulate different tasks
        self.shift = np.random.RandomState(client_id).uniform(-2, 2, dimension)

        super().__init__(
            dim=dimension,
            obj=1,
            x_lb=np.full(dimension, -10.0),
            x_ub=np.full(dimension, 10.0),
            fe_init=20,
            fe_max=max_fe
        )
        self.set_id(client_id)

    def _eval_single(self, x):
        """Shifted sphere function"""
        shifted_x = x - self.shift
        return np.sum(shifted_x ** 2)


def test_surrogate_models():
    """Test individual surrogate models"""
    print("Testing surrogate models...")
    
    # Generate test data
    np.random.seed(42)
    X = np.random.uniform(-5, 5, (20, 5))
    y = np.sum(X**2, axis=1) + np.random.normal(0, 0.1, 20)
    X_test = np.random.uniform(-5, 5, (10, 5))
    
    models = {
        'GP': GPModel(),
        'RBF': RBFModel(),
        'PRS': PRSModel(),
        'KNN': KNNModel()
    }
    
    for name, model in models.items():
        try:
            model.fit(X, y)
            predictions = model.predict(X_test)
            if isinstance(predictions, tuple):
                pred_mean, pred_std = predictions
                print(f"  ✓ {name} model: mean shape {pred_mean.shape}, std shape {pred_std.shape}")
            else:
                print(f"  ✓ {name} model: prediction shape {predictions.shape}")
        except Exception as e:
            print(f"  ✗ {name} model failed: {e}")


def test_infill_criteria():
    """Test infill criteria"""
    print("Testing infill criteria...")
    
    # Generate test data
    np.random.seed(42)
    X = np.random.uniform(-5, 5, (20, 5))
    y = np.sum(X**2, axis=1)
    candidates = np.random.uniform(-5, 5, (10, 5))
    bounds = np.array([[-5, 5]] * 5)
    f_min = np.min(y)
    
    # Test GP-based criteria
    gp_model = GPModel()
    gp_model.fit(X, y)
    
    lcb_criterion = LCBCriterion()
    ei_criterion = EICriterion()
    
    try:
        lcb_idx = lcb_criterion.select_solution(gp_model, candidates, X, f_min, bounds)
        ei_idx = ei_criterion.select_solution(gp_model, candidates, X, f_min, bounds)
        print(f"  ✓ LCB criterion selected index: {lcb_idx}")
        print(f"  ✓ EI criterion selected index: {ei_idx}")
    except Exception as e:
        print(f"  ✗ GP criteria failed: {e}")
    
    # Test RBF-based criteria
    rbf_model = RBFModel()
    rbf_model.fit(X, y)
    
    prescreening_criterion = PrescreeningCriterion()
    
    try:
        ps_idx = prescreening_criterion.select_solution(rbf_model, candidates, X, f_min, bounds)
        print(f"  ✓ Prescreening criterion selected index: {ps_idx}")
    except Exception as e:
        print(f"  ✗ RBF criteria failed: {e}")


def test_two_level_mab():
    """Test Two-Level Multi-Armed Bandit"""
    print("Testing Two-Level MAB...")
    
    mab = TwoLevelMAB(alpha=2.0)
    
    # Test initial selections (should use combinatorial arms)
    for t in range(1, 9):
        high_arm, low_arm = mab.select_arms(t)
        print(f"  Iteration {t}: {high_arm} + {low_arm}")
    
    # Test reward updates
    population = np.random.uniform(-5, 5, (20, 6))  # 20 solutions, 5D + fitness
    population[:, -1] = np.sum(population[:, :-1]**2, axis=1)  # Add fitness values
    
    new_solution = np.random.uniform(-5, 5, 5)
    new_fitness = np.sum(new_solution**2)
    
    try:
        mab.update_rewards('GP', 'LCB', population, new_solution, new_fitness)
        print(f"  ✓ Reward update successful")
        print(f"  GP value: {mab.high_values['GP']:.4f}")
        print(f"  LCB value: {mab.low_values['LCB']:.4f}")
    except Exception as e:
        print(f"  ✗ Reward update failed: {e}")


def test_de_operator():
    """Test Differential Evolution operator"""
    print("Testing DE operator...")
    
    de = DEOperator(F=0.5, CR=0.9)
    population = np.random.uniform(-5, 5, (10, 5))
    bounds = np.array([[-5, 5]] * 5)
    
    try:
        offspring = de.generate_offspring(population, bounds)
        print(f"  ✓ DE operator: population shape {population.shape}, offspring shape {offspring.shape}")
        
        # Check bounds
        within_bounds = np.all((offspring >= bounds[:, 0]) & (offspring <= bounds[:, 1]))
        print(f"  ✓ All offspring within bounds: {within_bounds}")
    except Exception as e:
        print(f"  ✗ DE operator failed: {e}")


def test_latin_hypercube_sampling():
    """Test Latin Hypercube Sampling"""
    print("Testing Latin Hypercube Sampling...")
    
    bounds = np.array([[-10, 10]] * 5)
    samples = latin_hypercube_sampling(20, 5, bounds)
    
    print(f"  ✓ LHS: generated {samples.shape[0]} samples in {samples.shape[1]}D")
    
    # Check bounds
    within_bounds = np.all((samples >= bounds[:, 0]) & (samples <= bounds[:, 1]))
    print(f"  ✓ All samples within bounds: {within_bounds}")


def run_client(client_id, dimension=10, max_fe=200):
    """Run a single client"""
    print(f"Starting client {client_id}")
    
    try:
        # Create problem
        problem = TestProblem(client_id, dimension, max_fe)
        
        # Create client
        client = AutoSAEAClient(
            problem=problem,
            population_size=20,
            alpha=2.0,
            F=0.5,
            CR=0.9,
            sync_interval=5
        )
        
        # Run optimization
        result = client.start()
        print(f"Client {client_id} completed with best fitness: {client.solutions.y_min:.6e}")
        return result
        
    except Exception as e:
        print(f"Client {client_id} failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None


def run_server():
    """Run the server"""
    print("Starting AUTOSAEA server")
    
    try:
        server = AutoSAEAServer(
            aggregation_strategy='weighted',
            min_clients_for_aggregation=2,
            global_strategy_weight=0.2
        )
        
        server.start()
        
    except Exception as e:
        print(f"Server failed with error: {e}")
        import traceback
        traceback.print_exc()


def test_single_client_standalone():
    """Test single client optimization without server"""
    print("\n" + "="*50)
    print("Testing Single Client Standalone Optimization")
    print("="*50)

    try:
        # Create problem
        problem = TestProblem(1, dimension=5, max_fe=50)

        # Create client
        client = AutoSAEAClient(
            problem=problem,
            population_size=10,
            alpha=2.0,
            F=0.5,
            CR=0.9,
            sync_interval=100,  # Set high to avoid server sync
            model_sync_interval=100
        )

        # Test individual optimization steps without server connection
        client._initialize_population()
        print(f"  ✓ Population initialized with {len(client.database)} solutions")

        # Test a few optimization iterations
        for i in range(5):
            if client.problem.fe_available > 0:
                client.optimize()

        best_fitness = client._get_best_fitness()
        print(f"  ✓ Optimization completed, best fitness: {best_fitness:.6e}")

        # Test model parameter extraction
        model_params = {}
        for model_name, model in client.models.items():
            if hasattr(model, 'is_fitted') and model.is_fitted:
                params = model.get_parameters()
                if params:
                    model_params[model_name] = params
                    print(f"  ✓ Extracted parameters from {model_name} model")

        print("✓ Single client standalone test passed")

    except Exception as e:
        print(f"✗ Single client standalone test failed: {e}")
        import traceback
        traceback.print_exc()


def test_model_parameter_aggregation():
    """Test model parameter aggregation functionality"""
    print("\n" + "="*50)
    print("Testing Model Parameter Aggregation")
    print("="*50)

    try:
        from algorithms.AUTOSAEA_pyfmto.autosaea_utils import aggregate_model_parameters

        # Create mock model parameters from different clients
        client1_params = {
            'GP': {
                'kernel_theta': np.array([1.0, 0.5]),
                'alpha': 1e-6
            },
            'PRS': {
                'coeffs': np.array([1.0, 2.0, 3.0])
            }
        }

        client2_params = {
            'GP': {
                'kernel_theta': np.array([1.2, 0.6]),
                'alpha': 1.2e-6
            },
            'PRS': {
                'coeffs': np.array([1.1, 2.1, 3.1])
            }
        }

        # Test aggregation
        aggregated = aggregate_model_parameters([client1_params, client2_params])

        print(f"  ✓ Aggregated GP kernel_theta: {aggregated['GP']['kernel_theta']}")
        print(f"  ✓ Aggregated GP alpha: {aggregated['GP']['alpha']}")
        print(f"  ✓ Aggregated PRS coeffs: {aggregated['PRS']['coeffs']}")

        print("✓ Model parameter aggregation test passed")

    except Exception as e:
        print(f"✗ Model parameter aggregation test failed: {e}")
        import traceback
        traceback.print_exc()


def test_multi_client():
    """Test multi-client federated optimization"""
    print("\n" + "="*50)
    print("Testing Multi-Client Federated Optimization")
    print("="*50)
    
    # Start server in separate process
    server_process = mp.Process(target=run_server)
    server_process.start()
    
    # Wait for server to start
    time.sleep(2)
    
    # Start multiple clients
    num_clients = 3
    client_processes = []
    
    for i in range(1, num_clients + 1):
        client_process = mp.Process(target=run_client, args=(i, 5, 100))
        client_processes.append(client_process)
        client_process.start()
    
    # Wait for all clients to complete
    for process in client_processes:
        process.join()
    
    # Terminate server
    server_process.terminate()
    server_process.join()
    
    print("✓ Multi-client test completed")


def main():
    """Main test function"""
    print("AUTOSAEA Algorithm Test Suite")
    print("="*50)
    
    # Test individual components
    test_surrogate_models()
    print()
    test_infill_criteria()
    print()
    test_two_level_mab()
    print()
    test_de_operator()
    print()
    test_latin_hypercube_sampling()
    
    # Test optimization
    test_single_client_standalone()

    # Test model parameter aggregation
    test_model_parameter_aggregation()
    
    # Uncomment to test multi-client (requires more setup)
    # test_multi_client()
    
    print("\n" + "="*50)
    print("All tests completed!")
    print("="*50)


if __name__ == "__main__":
    main()
