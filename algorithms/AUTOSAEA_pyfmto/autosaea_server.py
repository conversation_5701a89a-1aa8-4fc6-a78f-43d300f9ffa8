"""
AutoSAEA Server Implementation

This module implements the AutoSAEA server that integrates with the pyfmto framework.
The server performs federated aggregation of MAB experiences and distributes
global optimization strategies to clients.
"""

import numpy as np
import logging
from typing import Dict, List, Optional
from collections import defaultdict

from pyfmto.framework import Server, ClientPackage, ServerPackage
from .autosaea_utils import (
    AutoSAEAActions,
    TwoLevelMAB,
    aggregate_mab_experiences,
    aggregate_model_parameters
)

logger = logging.getLogger(__name__)


class AutoSAEAServer(Server):
    """
    aggregation_strategy: weighted
    min_clients_for_aggregation: 2
    global_strategy_weight: 0.2
    """

    def __init__(self, **kwargs):
        super().__init__()
        kwargs = self.update_kwargs(kwargs)

        # Server parameters
        self.aggregation_strategy = kwargs.get('aggregation_strategy', 'weighted')
        self.min_clients_for_aggregation = kwargs.get('min_clients_for_aggregation', 2)
        self.global_strategy_weight = kwargs.get('global_strategy_weight', 0.2)

        # Data storage
        self.client_mab_experiences = {}  # client_id -> MAB experience data
        self.client_model_parameters = {}  # client_id -> model parameters
        self.client_optimization_results = {}  # client_id -> optimization results
        self.global_mab_strategy = None
        self.global_model_parameters = None
        self.aggregation_history = []
        self.model_aggregation_history = []

        # Statistics
        self.aggregation_stats = {
            'total_aggregations': 0,
            'participating_clients': set(),
            'best_global_fitness': float('inf'),
            'convergence_history': []
        }

    def handle_request(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client requests"""
        action_handlers = {
            AutoSAEAActions.PUSH_MODEL_PARAMETERS: self._handle_push_model_parameters,
            AutoSAEAActions.PULL_GLOBAL_PARAMETERS: self._handle_pull_global_parameters,
            AutoSAEAActions.PUSH_MAB_EXPERIENCE: self._handle_push_mab_experience,
            AutoSAEAActions.PULL_GLOBAL_STRATEGY: self._handle_pull_global_strategy,
            AutoSAEAActions.PUSH_OPTIMIZATION_RESULT: self._handle_push_optimization_result,
            AutoSAEAActions.PULL_AGGREGATED_RESULT: self._handle_pull_aggregated_result,
        }

        handler = action_handlers.get(client_data.action)
        if handler:
            return handler(client_data)
        else:
            logger.warning(f"Unknown action: {client_data.action}")
            return ServerPackage('error', {'status': 'unknown_action'})

    def _handle_push_model_parameters(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pushing model parameters"""
        client_id = client_data.cid
        data = client_data.data

        if data and 'model_parameters' in data:
            self.client_model_parameters[client_id] = {
                'model_parameters': data['model_parameters'],
                'iteration': data.get('iteration', 0),
                'performance_weight': data.get('performance_weight', 1.0),
                'evaluations_used': data.get('evaluations_used', 0),
                'timestamp': data.get('timestamp', 0)
            }

            logger.debug(f"Received model parameters from client {client_id}")
            return ServerPackage('success', {'status': 'parameters_received'})
        else:
            return ServerPackage('error', {'status': 'invalid_parameter_data'})

    def _handle_pull_global_parameters(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pulling global model parameters"""
        client_id = client_data.cid

        # Check if we have enough clients for aggregation
        if len(self.client_model_parameters) >= self.min_clients_for_aggregation:
            # Perform aggregation if not done recently
            if self.global_model_parameters is None or self._should_reaggregate_models():
                self._aggregate_model_parameters()

            if self.global_model_parameters:
                parameter_data = {
                    'global_parameters': self.global_model_parameters,
                    'aggregation_round': len(self.model_aggregation_history),
                    'participating_clients': len(self.client_model_parameters)
                }
                return ServerPackage('success', parameter_data)

        # No global parameters available yet
        return ServerPackage('waiting', {'global_parameters': None})

    def _handle_push_mab_experience(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pushing MAB experience"""
        client_id = client_data.cid
        data = client_data.data

        if data and 'mab_state' in data:
            self.client_mab_experiences[client_id] = {
                'mab_state': data['mab_state'],
                'iteration': data.get('iteration', 0),
                'best_fitness': data.get('best_fitness', float('inf')),
                'evaluations_used': data.get('evaluations_used', 0),
                'timestamp': data.get('timestamp', 0)
            }
            
            # Update statistics
            self.aggregation_stats['participating_clients'].add(client_id)
            current_best = data.get('best_fitness', float('inf'))
            if current_best < self.aggregation_stats['best_global_fitness']:
                self.aggregation_stats['best_global_fitness'] = current_best

            logger.debug(f"Received MAB experience from client {client_id}")
            return ServerPackage('success', {'status': 'experience_received'})
        else:
            return ServerPackage('error', {'status': 'invalid_experience_data'})

    def _handle_pull_global_strategy(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pulling global strategy"""
        client_id = client_data.cid

        # Check if we have enough clients for aggregation
        if len(self.client_mab_experiences) >= self.min_clients_for_aggregation:
            # Perform aggregation if not done recently
            if self.global_mab_strategy is None or self._should_reaggregate():
                self._aggregate_mab_experiences()

            if self.global_mab_strategy:
                strategy_data = {
                    'global_strategy': self.global_mab_strategy,
                    'aggregation_round': self.aggregation_stats['total_aggregations'],
                    'participating_clients': len(self.aggregation_stats['participating_clients'])
                }
                return ServerPackage('success', strategy_data)

        # No global strategy available yet
        return ServerPackage('waiting', {'global_strategy': None})

    def _handle_push_optimization_result(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pushing final optimization results"""
        client_id = client_data.cid
        data = client_data.data

        if data:
            self.client_optimization_results[client_id] = data
            logger.info(f"Received optimization result from client {client_id}")
            return ServerPackage('success', {'status': 'result_received'})
        else:
            return ServerPackage('error', {'status': 'invalid_result_data'})

    def _handle_pull_aggregated_result(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pulling aggregated results"""
        if len(self.client_optimization_results) >= self.num_clients:
            # All results received, compute aggregated statistics
            aggregated_results = self._compute_aggregated_results()
            return ServerPackage('success', aggregated_results)
        else:
            return ServerPackage('waiting', {'status': 'waiting_for_all_clients'})

    def _aggregate_mab_experiences(self):
        """Aggregate MAB experiences from all clients"""
        if len(self.client_mab_experiences) < self.min_clients_for_aggregation:
            return

        # Extract MAB states from all clients
        mab_states = []
        client_weights = []
        
        for client_id, exp_data in self.client_mab_experiences.items():
            mab_states.append(exp_data['mab_state'])
            
            # Weight by inverse of best fitness (better clients have more influence)
            fitness = exp_data.get('best_fitness', float('inf'))
            if fitness == float('inf'):
                weight = 1.0
            else:
                weight = 1.0 / (1.0 + abs(fitness))
            client_weights.append(weight)

        # Normalize weights
        total_weight = sum(client_weights)
        if total_weight > 0:
            client_weights = [w / total_weight for w in client_weights]
        else:
            client_weights = [1.0 / len(client_weights)] * len(client_weights)

        # Aggregate based on strategy
        if self.aggregation_strategy == 'average':
            self.global_mab_strategy = aggregate_mab_experiences(mab_states)
        elif self.aggregation_strategy == 'weighted':
            self.global_mab_strategy = self._weighted_aggregate_mab_experiences(
                mab_states, client_weights
            )

        # Update statistics
        self.aggregation_stats['total_aggregations'] += 1
        self.aggregation_history.append({
            'round': self.aggregation_stats['total_aggregations'],
            'participating_clients': len(self.client_mab_experiences),
            'global_strategy': self.global_mab_strategy.copy() if self.global_mab_strategy else None
        })

        logger.info(f"Aggregated MAB experiences from {len(self.client_mab_experiences)} clients")

    def _aggregate_model_parameters(self):
        """Aggregate model parameters from all clients"""
        if len(self.client_model_parameters) < self.min_clients_for_aggregation:
            return

        # Extract model parameters and weights from all clients
        model_params_list = []
        client_weights = []

        for client_id, param_data in self.client_model_parameters.items():
            model_params_list.append(param_data['model_parameters'])

            # Use performance weight (better performance = higher weight)
            weight = param_data.get('performance_weight', 1.0)
            client_weights.append(weight)

        # Aggregate model parameters
        self.global_model_parameters = aggregate_model_parameters(model_params_list, client_weights)

        # Update aggregation history
        self.model_aggregation_history.append({
            'round': len(self.model_aggregation_history) + 1,
            'participating_clients': len(self.client_model_parameters),
            'global_parameters': self.global_model_parameters.copy() if self.global_model_parameters else None
        })

        logger.info(f"Aggregated model parameters from {len(self.client_model_parameters)} clients")

    def _should_reaggregate_models(self) -> bool:
        """Determine if model reaggregation is needed"""
        # Simple strategy: reaggregate every few client updates
        return len(self.client_model_parameters) % max(2, self.num_clients // 2) == 0

    def _weighted_aggregate_mab_experiences(self, mab_states: List[Dict], 
                                          weights: List[float]) -> Dict:
        """Aggregate MAB experiences with client weights"""
        if not mab_states:
            return {}

        # Initialize aggregated state
        aggregated = {
            'high_values': {},
            'low_values': {},
            'high_counts': {},
            'low_counts': {}
        }

        # Get all arms from first state
        first_state = mab_states[0]
        high_arms = list(first_state['high_values'].keys())
        low_arms = list(first_state['low_values'].keys())

        # Initialize with zeros
        for arm in high_arms:
            aggregated['high_values'][arm] = 0.0
            aggregated['high_counts'][arm] = 0

        for arm in low_arms:
            aggregated['low_values'][arm] = 0.0
            aggregated['low_counts'][arm] = 0

        # Weighted aggregation
        for state, weight in zip(mab_states, weights):
            for arm in high_arms:
                count = state['high_counts'][arm]
                value = state['high_values'][arm]
                aggregated['high_counts'][arm] += count
                aggregated['high_values'][arm] += weight * count * value

            for arm in low_arms:
                count = state['low_counts'][arm]
                value = state['low_values'][arm]
                aggregated['low_counts'][arm] += count
                aggregated['low_values'][arm] += weight * count * value

        # Calculate weighted averages
        for arm in high_arms:
            if aggregated['high_counts'][arm] > 0:
                aggregated['high_values'][arm] /= aggregated['high_counts'][arm]

        for arm in low_arms:
            if aggregated['low_counts'][arm] > 0:
                aggregated['low_values'][arm] /= aggregated['low_counts'][arm]

        return aggregated

    def _should_reaggregate(self) -> bool:
        """Determine if reaggregation is needed"""
        # Simple strategy: reaggregate every few client updates
        return len(self.client_mab_experiences) % max(2, self.num_clients // 2) == 0

    def _compute_aggregated_results(self) -> Dict:
        """Compute aggregated optimization results"""
        if not self.client_optimization_results:
            return {}

        best_fitness_values = []
        total_evaluations = 0
        
        for client_id, result in self.client_optimization_results.items():
            best_fitness = result.get('best_fitness', float('inf'))
            evaluations = result.get('evaluations_used', 0)
            
            best_fitness_values.append(best_fitness)
            total_evaluations += evaluations

        aggregated_results = {
            'total_clients': len(self.client_optimization_results),
            'global_best_fitness': min(best_fitness_values) if best_fitness_values else float('inf'),
            'global_worst_fitness': max(best_fitness_values) if best_fitness_values else float('inf'),
            'average_best_fitness': np.mean(best_fitness_values) if best_fitness_values else float('inf'),
            'fitness_std': np.std(best_fitness_values) if best_fitness_values else 0.0,
            'total_evaluations': total_evaluations,
            'average_evaluations_per_client': total_evaluations / len(self.client_optimization_results) if self.client_optimization_results else 0,
            'aggregation_rounds': self.aggregation_stats['total_aggregations']
        }

        logger.info(f"Computed aggregated results: best={aggregated_results['global_best_fitness']:.6e}")
        return aggregated_results

    def aggregate(self, client_id: int):
        """Aggregate data (called by framework)"""
        # Most aggregation is handled in the request handlers
        # This method can be used for periodic aggregation if needed
        if len(self.client_mab_experiences) >= self.min_clients_for_aggregation:
            if self.global_mab_strategy is None:
                self._aggregate_mab_experiences()

        # Also aggregate model parameters if available
        if len(self.client_model_parameters) >= self.min_clients_for_aggregation:
            if self.global_model_parameters is None:
                self._aggregate_model_parameters()

    def get_server_stats(self) -> Dict:
        """Get server statistics"""
        return {
            'aggregation_stats': self.aggregation_stats,
            'total_clients_participated': len(self.aggregation_stats['participating_clients']),
            'total_aggregation_rounds': self.aggregation_stats['total_aggregations'],
            'current_global_best': self.aggregation_stats['best_global_fitness'],
            'has_global_strategy': self.global_mab_strategy is not None,
            'has_global_model_parameters': self.global_model_parameters is not None,
            'total_model_aggregation_rounds': len(self.model_aggregation_history),
            'clients_with_model_params': len(self.client_model_parameters)
        }
