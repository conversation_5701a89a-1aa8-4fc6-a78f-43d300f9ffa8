"""
AutoSAEA Client Implementation

This module implements the AutoSAEA client that integrates with the pyfmto framework.
The client performs surrogate-assisted evolutionary optimization with automatic
model and infill criterion configuration using Two-Level Multi-Armed Bandit.
"""

import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm

from pyfmto.framework import Client, record_runtime, ClientPackage, ServerPackage
from .autosaea_utils import (
    AutoSAEAActions,
    TwoLevelMAB,
    DEOperator,
    create_model_criterion_mapping,
    latin_hypercube_sampling,
    aggregate_model_parameters
)

logger = logging.getLogger(__name__)


class AutoSAEAClient(Client):
    """
    population_size: 50
    alpha: 2.0
    F: 0.5
    CR: 0.9
    sync_interval: 10
    """

    def __init__(self, problem, **kwargs):
        super().__init__(problem)
        kwargs = self.update_kwargs(kwargs)

        # Algorithm parameters
        self.population_size = kwargs.get('population_size', 50)
        self.alpha = kwargs.get('alpha', 2.0)
        self.F = kwargs.get('F', 0.5)
        self.CR = kwargs.get('CR', 0.9)
        self.sync_interval = kwargs.get('sync_interval', 10)
        self.model_sync_interval = kwargs.get('model_sync_interval', 20)  # Separate interval for model sync

        # Initialize components
        self.mab = TwoLevelMAB(alpha=self.alpha)
        self.de_operator = DEOperator(F=self.F, CR=self.CR)
        self.model_criterion_mapping = create_model_criterion_mapping()

        # Initialize models and criteria
        self.models = {}
        self.criteria = {}
        for model_name, config in self.model_criterion_mapping.items():
            self.models[model_name] = config['model_class']()
            for criterion_name, criterion_class in config['criteria'].items():
                self.criteria[criterion_name] = criterion_class()

        # State variables
        self.database = []  # Store all evaluated solutions
        self.current_population = None
        self.iteration = 0
        self.last_sync_iteration = 0
        self.last_model_sync_iteration = 0
        
        # History tracking
        self.history = {
            'best_fitness': [],
            'selected_models': [],
            'selected_criteria': [],
            'mab_values': []
        }

    def start(self):
        """Override start method with progress bar"""
        try:
            logger.info(f"{self.name} started")
            self._Client__register_id()
            self._Client__logging_params()

            # Initialize population
            self._initialize_population()

            # Progress bar
            pbar = tqdm(
                total=self.fe_max,
                desc=f"Client {self.id}",
                unit="eval",
                ncols=80,
                position=None,
                leave=True,
                dynamic_ncols=True
            )

            prev_fe_used = 0
            while self.problem.fe_available > 0:
                self.optimize()
                current_fe_used = self.fe_max - self.problem.fe_available
                fe_increment = current_fe_used - prev_fe_used
                if fe_increment > 0:
                    pbar.update(fe_increment)
                    prev_fe_used = current_fe_used
                self._Client__logging_round_info(only_latest=True)

            pbar.close()
            self._Client__logging_round_info(only_latest=False)
            self.send_quit()
            logger.info(f"{self.name} exit with available FE = {self.problem.fe_available}")
        except Exception:
            self.send_quit()
            if self.id == 1:
                print(f"Traceback of {self.name}")
                import traceback
                traceback.print_exc()
                logger.error(traceback.format_exc())
            logger.info(f"{self.name} exit with available FE = {self.problem.fe_available}")
            exit(-1)
        return self.id, self.solutions

    def optimize(self):
        """Main optimization loop"""
        self.iteration += 1
        
        # Get current best population
        self._update_current_population()
        
        # Select model and criterion using MAB
        model_name, criterion_name = self.mab.select_arms(self.iteration)
        
        # Generate new solution
        new_solution = self._generate_new_solution(model_name, criterion_name)
        
        # Evaluate new solution
        if self.problem.fe_available > 0:
            fitness_result = self.problem.evaluate(new_solution.reshape(1, -1))
            # Handle different fitness result formats
            if isinstance(fitness_result, np.ndarray):
                if fitness_result.ndim > 0:
                    new_fitness = float(fitness_result.flatten()[0])
                else:
                    new_fitness = float(fitness_result)
            else:
                new_fitness = float(fitness_result)
            self.database.append(np.concatenate([new_solution, [new_fitness]]))
            
            # Update MAB rewards
            self.mab.update_rewards(model_name, criterion_name, 
                                  self.current_population, new_solution, new_fitness)
            
            # Update history
            self.history['best_fitness'].append(self._get_best_fitness())
            self.history['selected_models'].append(model_name)
            self.history['selected_criteria'].append(criterion_name)
            self.history['mab_values'].append(self.mab.get_state())
            
            # Sync with server periodically
            if self.iteration - self.last_sync_iteration >= self.sync_interval:
                self._sync_mab_with_server()
                self.last_sync_iteration = self.iteration

            # Sync model parameters periodically
            if self.iteration - self.last_model_sync_iteration >= self.model_sync_interval:
                self._sync_models_with_server()
                self.last_model_sync_iteration = self.iteration

    @record_runtime("Initialize Population")
    def _initialize_population(self):
        """Initialize population using Latin Hypercube Sampling"""
        bounds = np.column_stack([self.x_lb, self.x_ub])
        initial_population = latin_hypercube_sampling(
            self.population_size, self.dim, bounds
        )
        
        # Evaluate initial population
        for x in initial_population:
            if self.problem.fe_available > 0:
                fitness_result = self.problem.evaluate(x.reshape(1, -1))
                # Handle different fitness result formats
                if isinstance(fitness_result, np.ndarray):
                    if fitness_result.ndim > 0:
                        fitness = float(fitness_result.flatten()[0])
                    else:
                        fitness = float(fitness_result)
                else:
                    fitness = float(fitness_result)
                self.database.append(np.concatenate([x, [fitness]]))
        
        logger.info(f"{self.name} initialized with {len(self.database)} solutions")

    def _update_current_population(self):
        """Update current population with best N solutions from database"""
        if len(self.database) <= self.population_size:
            data = np.array(self.database)
        else:
            # Sort by fitness and take best N
            data = np.array(self.database)
            sorted_indices = np.argsort(data[:, -1])
            data = data[sorted_indices[:self.population_size]]
        
        self.current_population = data

    def _generate_new_solution(self, model_name: str, criterion_name: str) -> np.ndarray:
        """Generate new solution using selected model and criterion"""
        if self.current_population is None or len(self.current_population) == 0:
            # Fallback to random solution
            bounds = np.column_stack([self.x_lb, self.x_ub])
            return np.random.uniform(bounds[:, 0], bounds[:, 1])
        
        # Get population data
        X_pop = self.current_population[:, :-1]
        y_pop = self.current_population[:, -1]
        f_min = np.min(y_pop)
        
        # Train the selected model
        model = self.models[model_name]
        try:
            model.fit(X_pop, y_pop)
        except Exception as e:
            logger.warning(f"Model {model_name} fitting failed: {e}")
            # Fallback to random solution
            bounds = np.column_stack([self.x_lb, self.x_ub])
            return np.random.uniform(bounds[:, 0], bounds[:, 1])
        
        # Generate candidate solutions using DE
        bounds = np.column_stack([self.x_lb, self.x_ub])
        candidates = self.de_operator.generate_offspring(X_pop, bounds)
        
        # Select best candidate using the criterion
        criterion = self.criteria[criterion_name]
        try:
            best_idx = criterion.select_solution(model, candidates, X_pop, f_min, bounds)
            return candidates[best_idx]
        except Exception as e:
            logger.warning(f"Criterion {criterion_name} selection failed: {e}")
            # Fallback to best predicted candidate
            if hasattr(model, 'predict'):
                try:
                    predictions = model.predict(candidates)
                    if isinstance(predictions, tuple):
                        predictions = predictions[0]
                    return candidates[np.argmin(predictions)]
                except:
                    pass
            
            # Final fallback to random candidate
            return candidates[np.random.randint(len(candidates))]

    def _get_best_fitness(self) -> float:
        """Get current best fitness value"""
        if not self.database:
            return float('inf')
        return min(sol[-1] for sol in self.database)

    @record_runtime("MAB Server Sync")
    def _sync_mab_with_server(self):
        """Synchronize MAB experience with server"""
        try:
            # Push MAB experience to server
            mab_data = {
                'client_id': self.id,
                'iteration': self.iteration,
                'mab_state': self.mab.get_state(),
                'best_fitness': self._get_best_fitness(),
                'evaluations_used': len(self.database)
            }
            
            pkg = ClientPackage(self.id, AutoSAEAActions.PUSH_MAB_EXPERIENCE, mab_data)
            response = self.request_server(pkg)
            
            if response and response.data:
                logger.debug(f"{self.name} MAB experience pushed successfully")
            
            # Pull global strategy from server
            pkg = ClientPackage(self.id, AutoSAEAActions.PULL_GLOBAL_STRATEGY, None)
            response = self.request_server(pkg, repeat=3)
            
            if response and response.data and 'global_strategy' in response.data:
                global_strategy = response.data['global_strategy']
                if global_strategy:
                    # Optionally update local MAB with global strategy
                    self._update_with_global_strategy(global_strategy)
                    logger.debug(f"{self.name} received global strategy")
                    
        except Exception as e:
            logger.warning(f"{self.name} server sync failed: {e}")

    def _update_with_global_strategy(self, global_strategy: Dict):
        """Update local MAB with global strategy (optional)"""
        # This is a simple implementation that averages local and global values
        # More sophisticated strategies can be implemented here
        alpha = 0.1  # Weight for global strategy
        
        for arm in self.mab.high_arms:
            if arm in global_strategy.get('high_values', {}):
                global_value = global_strategy['high_values'][arm]
                local_value = self.mab.high_values[arm]
                self.mab.high_values[arm] = (1 - alpha) * local_value + alpha * global_value
        
        for arm in self.mab.low_arms:
            if arm in global_strategy.get('low_values', {}):
                global_value = global_strategy['low_values'][arm]
                local_value = self.mab.low_values[arm]
                self.mab.low_values[arm] = (1 - alpha) * local_value + alpha * global_value

    @record_runtime("Model Server Sync")
    def _sync_models_with_server(self):
        """Synchronize model parameters with server"""
        try:
            # Extract parameters from all fitted models
            model_params = {}
            for model_name, model in self.models.items():
                if hasattr(model, 'is_fitted') and model.is_fitted:
                    model_params[model_name] = model.get_parameters()

            if not model_params:
                logger.debug(f"{self.name} no fitted models to sync")
                return

            # Push model parameters to server
            sync_data = {
                'client_id': self.id,
                'iteration': self.iteration,
                'model_parameters': model_params,
                'performance_weight': 1.0 / (1.0 + self._get_best_fitness()),  # Better performance = higher weight
                'evaluations_used': len(self.database)
            }

            pkg = ClientPackage(self.id, AutoSAEAActions.PUSH_MODEL_PARAMETERS, sync_data)
            response = self.request_server(pkg)

            if response and response.data:
                logger.debug(f"{self.name} model parameters pushed successfully")

            # Pull global model parameters from server
            pkg = ClientPackage(self.id, AutoSAEAActions.PULL_GLOBAL_PARAMETERS, None)
            response = self.request_server(pkg, repeat=3)

            if response and response.data and 'global_parameters' in response.data:
                global_params = response.data['global_parameters']
                if global_params:
                    self._update_models_with_global_parameters(global_params)
                    logger.debug(f"{self.name} received and applied global model parameters")

        except Exception as e:
            logger.warning(f"{self.name} model sync failed: {e}")

    def _update_models_with_global_parameters(self, global_params: Dict):
        """Update local models with global parameters"""
        for model_name, params in global_params.items():
            if model_name in self.models and params:
                try:
                    self.models[model_name].set_parameters(params)
                    logger.debug(f"{self.name} updated {model_name} model parameters")
                except Exception as e:
                    logger.warning(f"{self.name} failed to update {model_name} parameters: {e}")

    def check_pkg(self, x: ServerPackage) -> bool:
        """Check if server package is acceptable"""
        if x is None:
            return False
        return x.data is not None

    @property
    def x(self) -> np.ndarray:
        """Get current solutions"""
        return self.solutions.x
