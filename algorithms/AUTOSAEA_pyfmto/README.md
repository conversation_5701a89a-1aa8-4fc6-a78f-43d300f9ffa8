# AUTOSAEA for pyfmto Framework

## Overview

AUTOSAEA (Surrogate-Assisted Evolutionary Algorithm with Auto-Configuration) is an advanced optimization algorithm that automatically selects the best combination of surrogate models and infill criteria using a Two-Level Multi-Armed Bandit (TL-MAB) approach. This implementation is adapted for the pyfmto federated multi-task optimization framework.

## Key Features

- **Automatic Model Selection**: Automatically chooses between GP, RBF, PRS, and KNN surrogate models
- **Adaptive Criterion Selection**: Dynamically selects optimal infill criteria for each model
- **Two-Level Multi-Armed Bandit**: Hierarchical online learning for model/criterion selection
- **Federated Learning**: Supports distributed optimization across multiple clients
- **Robust Performance**: Effective across diverse optimization landscapes

## Algorithm Components

### Surrogate Models
1. **GP (Gaussian Process)**: Provides uncertainty quantification
2. **RBF (Radial Basis Function)**: Fast and accurate for smooth functions
3. **PRS (Polynomial Response Surface)**: Effective for low-dimensional problems
4. **KNN (K-Nearest Neighbors)**: Classification-based approach for level prediction

### Infill Criteria
- **LCB/EI** (for GP): Lower Confidence Bound and Expected Improvement
- **Prescreening/LocalSearch** (for RBF/PRS): Direct optimization approaches
- **L1-Exploitation/L1-Exploration** (for KNN): Level-based selection strategies

### Two-Level MAB
- **High-level arms**: Surrogate models (GP, RBF, PRS, KNN)
- **Low-level arms**: Infill criteria associated with each model
- **TL-UCB**: Two-level upper confidence bound for arm selection
- **TL-R**: Two-level reward mechanism for performance feedback

## Usage

### Basic Usage

```python
from pyfmto.problems.benchmarks import Sphere
from algorithms.AUTOSAEA_pyfmto import AutoSAEAClient, AutoSAEAServer

# Create a test problem
problem = Sphere(dim=10, fe_max=500)

# Create client
client = AutoSAEAClient(
    problem=problem,
    population_size=50,
    alpha=2.0,
    F=0.5,
    CR=0.9,
    sync_interval=10
)

# Create server
server = AutoSAEAServer(
    aggregation_strategy='weighted',
    min_clients_for_aggregation=2,
    global_strategy_weight=0.2
)

# Run optimization
server.start()  # Start server in separate process
result = client.start()  # Run client optimization
```

### Parameters

#### Client Parameters

- `population_size`: Population size for optimization (default: 50)
- `alpha`: UCB parameter for exploration-exploitation balance (default: 2.0)
- `F`: Differential evolution mutation factor (default: 0.5)
- `CR`: Differential evolution crossover probability (default: 0.9)
- `sync_interval`: Interval for MAB strategy synchronization (default: 10)
- `model_sync_interval`: Interval for model parameter synchronization (default: 20)

#### Server Parameters

- `aggregation_strategy`: Strategy for MAB aggregation ('average', 'weighted')
- `min_clients_for_aggregation`: Minimum clients needed for aggregation (default: 2)
- `global_strategy_weight`: Weight for global strategy influence (default: 0.2)

## Algorithm Workflow

1. **Initialization**: Generate initial population using Latin Hypercube Sampling
2. **Model/Criterion Selection**: Use TL-UCB to select surrogate model and infill criterion
3. **Surrogate Training**: Train selected model on current population
4. **Candidate Generation**: Generate offspring using Differential Evolution
5. **Solution Selection**: Apply selected infill criterion to choose best candidate
6. **Evaluation**: Evaluate selected solution on expensive objective function
7. **Reward Update**: Update MAB rewards using TL-R mechanism
8. **Server Synchronization**: Periodically sync MAB experience with server
9. **Repeat**: Continue until budget exhausted

## Federated Learning Mechanism

### Enhanced Client-Server Communication

#### Model Parameter Sharing
- **PUSH_MODEL_PARAMETERS**: Clients upload surrogate model parameters (GP kernel params, RBF weights, PRS coefficients, KNN hyperparameters)
- **PULL_GLOBAL_PARAMETERS**: Clients download aggregated global model parameters

#### MAB Strategy Sharing
- **PUSH_MAB_EXPERIENCE**: Clients upload local MAB experiences
- **PULL_GLOBAL_STRATEGY**: Clients download aggregated global MAB strategy

#### Results Sharing
- **PUSH_OPTIMIZATION_RESULT**: Clients upload final optimization results
- **PULL_AGGREGATED_RESULT**: Clients download aggregated performance statistics

### Model Parameter Aggregation

#### Supported Model Types
1. **GP Models**: Kernel hyperparameters (theta), noise parameters (alpha)
2. **RBF Models**: Weights, polynomial coefficients, scaler parameters
3. **PRS Models**: Polynomial coefficients, scaler parameters
4. **KNN Models**: Hyperparameters (k, n_levels), scaler parameters

#### Aggregation Strategies
1. **Weighted Averaging**: Performance-weighted aggregation (better performing clients have higher influence)
2. **Privacy-Preserving**: Only model parameters are shared, not raw data
3. **Adaptive Synchronization**: Separate sync intervals for MAB strategies and model parameters

### Privacy Protection Features

- **No Raw Data Sharing**: Only model parameters and MAB statistics are exchanged
- **Performance-Based Weighting**: Better performing clients contribute more to global models
- **Selective Parameter Sharing**: Only essential model parameters are shared
- **Local Model Autonomy**: Clients maintain local model instances and can fall back to local optimization

## Performance Characteristics

- **Convergence**: Fast convergence due to adaptive model/criterion selection
- **Robustness**: Effective across diverse problem landscapes
- **Scalability**: Supports federated optimization with multiple clients
- **Efficiency**: Automatic configuration reduces manual parameter tuning

## Implementation Notes

- Based on the paper by Xie et al., IEEE TEVC 2024
- Fully compatible with pyfmto framework
- Includes comprehensive error handling and logging
- Supports both single-client and multi-client optimization

## References

```bibtex
@article{xie2024autosaea,
  title={Surrogate-Assisted Evolutionary Algorithm With Model and Infill Criterion Auto-Configuration},
  author={Xie, Lindong and Li, Genghui and Wang, Zhenkun and Cui, Laizhong and Gong, Maoguo},
  journal={IEEE Transactions on Evolutionary Computation},
  year={2024},
  publisher={IEEE}
}
```

## License

This implementation is part of the pyfmto framework and follows the same licensing terms.
