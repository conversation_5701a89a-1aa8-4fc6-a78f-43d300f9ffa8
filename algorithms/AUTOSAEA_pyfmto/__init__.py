"""
AUTOSAEA (Surrogate-Assisted Evolutionary Algorithm with Auto-Configuration) for pyfmto

This module implements the AUTOSAEA algorithm adapted for the pyfmto federated
multi-task optimization framework. The algorithm features automatic configuration
of surrogate models and infill criteria using Two-Level Multi-Armed Bandit (TL-MAB).

Key Components:
- AutoSAEAClient: Federated client implementing AUTOSAEA optimization
- AutoSAEAServer: Federated server for MAB strategy aggregation
- Surrogate models: GP, RBF, PRS, KNN
- Infill criteria: LCB, EI, Prescreening, LocalSearch, L1-Exploitation, L1-Exploration
- TwoLevelMAB: Two-level multi-armed bandit for model/criterion selection

Based on the paper:
"Surrogate-Assisted Evolutionary Algorithm With Model and Infill Criterion Auto-Configuration"
by <PERSON><PERSON> et al., IEEE Transactions on Evolutionary Computation, 2024.

Authors: <AUTHORS>
"""

from .autosaea_client import AutoSAEAClient
from .autosaea_server import AutoSAEAServer

__all__ = ['AutoSAEAClient', 'AutoSAEAServer']

# Version information
__version__ = '1.0.0'
__author__ = 'Adapted for pyfmto framework'
__email__ = '<EMAIL>'

# Algorithm metadata
ALGORITHM_INFO = {
    'name': 'AUTOSAEA',
    'full_name': 'Surrogate-Assisted Evolutionary Algorithm with Auto-Configuration',
    'type': 'Surrogate-Assisted Evolutionary Algorithm',
    'paradigm': 'Federated Multi-Task Optimization',
    'paper': 'Xie et al., IEEE TEVC 2024',
    'key_features': [
        'Automatic model and criterion selection',
        'Two-Level Multi-Armed Bandit (TL-MAB)',
        'Multiple surrogate models (GP, RBF, PRS, KNN)',
        'Federated learning capability',
        'Adaptive exploration-exploitation balance'
    ],
    'supported_problems': [
        'Continuous optimization',
        'Expensive optimization problems',
        'Multi-task optimization',
        'Federated optimization'
    ]
}
