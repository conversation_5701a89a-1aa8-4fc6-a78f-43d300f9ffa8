"""
AUTOSAEA Utilities Module

This module implements the core components of the AUTOSAEA algorithm for integration
with the pyfmto framework. It includes surrogate models, infill criteria, 
Two-Level Multi-Armed Bandit (TL-MAB), and differential evolution operators.

Based on the paper:
"Surrogate-Assisted Evolutionary Algorithm With Model and Infill Criterion Auto-Configuration"
by <PERSON><PERSON> et al., IEEE Transactions on Evolutionary Computation, 2024.
"""

import numpy as np
import warnings
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Callable, Union
from enum import Enum, auto
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, ConstantKernel as C
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler
from scipy.optimize import minimize
from scipy.spatial.distance import cdist
from scipy.stats import norm

warnings.filterwarnings('ignore')


class AutoSAEAActions(Enum):
    """Actions for AUTOSAEA client-server communication"""
    PUSH_MODEL_PARAMETERS = auto()   # Upload surrogate model parameters
    PULL_GLOBAL_PARAMETERS = auto()  # Download aggregated model parameters
    PUSH_MAB_EXPERIENCE = auto()     # Upload MAB experience
    PULL_GLOBAL_STRATEGY = auto()    # Download global MAB strategy
    PUSH_OPTIMIZATION_RESULT = auto() # Upload optimization result
    PULL_AGGREGATED_RESULT = auto()  # Download aggregated result


class SurrogateModel(ABC):
    """Abstract base class for surrogate models"""
    
    @abstractmethod
    def fit(self, X: np.ndarray, y: np.ndarray):
        """Train the surrogate model"""
        pass
    
    @abstractmethod
    def predict(self, X: np.ndarray) -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray]]:
        """Predict using the surrogate model"""
        pass


class GPModel(SurrogateModel):
    """Gaussian Process Model with uncertainty quantification"""
    
    def __init__(self, alpha: float = 1e-6, normalize_y: bool = True):
        """
        Initialize GP model
        
        Args:
            alpha: Noise level parameter
            normalize_y: Whether to normalize target values
        """
        kernel = C(1.0, (1e-3, 1e3)) * RBF(1.0, (1e-2, 1e2))
        self.model = GaussianProcessRegressor(
            kernel=kernel, 
            alpha=alpha, 
            normalize_y=normalize_y,
            random_state=42
        )
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler() if not normalize_y else None
        self.is_fitted = False
        
    def fit(self, X: np.ndarray, y: np.ndarray):
        """Train the GP model"""
        if len(X) == 0:
            raise ValueError("Cannot fit GP model with empty data")
            
        X_scaled = self.scaler_X.fit_transform(X)
        
        if self.scaler_y is not None:
            y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).ravel()
        else:
            y_scaled = y
            
        self.model.fit(X_scaled, y_scaled)
        self.is_fitted = True
        
    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Predict mean and standard deviation"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
            
        X_scaled = self.scaler_X.transform(X)
        y_pred_scaled, std_scaled = self.model.predict(X_scaled, return_std=True)
        
        if self.scaler_y is not None:
            y_pred = self.scaler_y.inverse_transform(y_pred_scaled.reshape(-1, 1)).ravel()
            # Approximate std scaling
            std = std_scaled * self.scaler_y.scale_[0]
        else:
            y_pred = y_pred_scaled
            std = std_scaled
            
        return y_pred, std

    def get_parameters(self) -> Dict:
        """Extract GP model parameters for federated learning"""
        if not self.is_fitted:
            return {}

        params = {
            'kernel_params': self.model.kernel_.get_params(),
            'alpha': self.model.alpha,
            'normalize_y': self.model.normalize_y,
            'n_restarts_optimizer': self.model.n_restarts_optimizer
        }

        # Add kernel hyperparameters if available
        if hasattr(self.model.kernel_, 'theta'):
            params['kernel_theta'] = self.model.kernel_.theta.copy()

        return params

    def set_parameters(self, params: Dict):
        """Set GP model parameters from federated aggregation"""
        if not params:
            return

        # Update kernel parameters
        if 'kernel_theta' in params:
            try:
                self.model.kernel_.theta = params['kernel_theta'].copy()
            except:
                pass  # Skip if incompatible

        # Update other parameters
        if 'alpha' in params:
            self.model.alpha = params['alpha']


class RBFModel(SurrogateModel):
    """Radial Basis Function Model with cubic kernel"""
    
    def __init__(self):
        """Initialize RBF model"""
        self.weights = None
        self.poly_coeffs = None
        self.X_train = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        self.is_fitted = False
        
    def _rbf_kernel(self, X1: np.ndarray, X2: np.ndarray) -> np.ndarray:
        """Cubic RBF kernel: ||x1 - x2||^3"""
        distances = cdist(X1, X2, 'euclidean')
        return distances ** 3
        
    def fit(self, X: np.ndarray, y: np.ndarray):
        """Train the RBF model"""
        if len(X) == 0:
            raise ValueError("Cannot fit RBF model with empty data")
            
        self.X_train = self.scaler_X.fit_transform(X)
        y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).ravel()
        
        n, d = X.shape
        
        # Build the system matrix [Phi P; P^T 0]
        Phi = self._rbf_kernel(self.X_train, self.X_train)
        P = np.column_stack([np.ones(n), self.X_train])
        
        # Construct the full system
        A = np.block([[Phi, P], [P.T, np.zeros((d+1, d+1))]])
        b = np.concatenate([y_scaled, np.zeros(d+1)])
        
        # Solve the system
        try:
            solution = np.linalg.solve(A, b)
        except np.linalg.LinAlgError:
            # Use pseudo-inverse if singular
            solution = np.linalg.pinv(A) @ b
            
        self.weights = solution[:n]
        self.poly_coeffs = solution[n:]
        self.is_fitted = True
            
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Predict using RBF model"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
            
        X_scaled = self.scaler_X.transform(X)
        n_test = X.shape[0]
        
        # RBF part
        Phi_test = self._rbf_kernel(X_scaled, self.X_train)
        rbf_part = Phi_test @ self.weights
        
        # Polynomial part
        P_test = np.column_stack([np.ones(n_test), X_scaled])
        poly_part = P_test @ self.poly_coeffs
        
        y_pred_scaled = rbf_part + poly_part
        y_pred = self.scaler_y.inverse_transform(y_pred_scaled.reshape(-1, 1)).ravel()

        return y_pred

    def get_parameters(self) -> Dict:
        """Extract RBF model parameters for federated learning"""
        if not self.is_fitted:
            return {}

        params = {
            'weights': self.weights.copy() if self.weights is not None else None,
            'poly_coeffs': self.poly_coeffs.copy() if self.poly_coeffs is not None else None,
            'X_train_shape': self.X_train.shape if self.X_train is not None else None,
            'scaler_X_params': {
                'mean_': self.scaler_X.mean_.copy() if hasattr(self.scaler_X, 'mean_') else None,
                'scale_': self.scaler_X.scale_.copy() if hasattr(self.scaler_X, 'scale_') else None
            },
            'scaler_y_params': {
                'mean_': self.scaler_y.mean_.copy() if hasattr(self.scaler_y, 'mean_') else None,
                'scale_': self.scaler_y.scale_.copy() if hasattr(self.scaler_y, 'scale_') else None
            }
        }

        return params

    def set_parameters(self, params: Dict):
        """Set RBF model parameters from federated aggregation"""
        if not params:
            return

        # Update weights and coefficients
        if 'weights' in params and params['weights'] is not None:
            self.weights = params['weights'].copy()

        if 'poly_coeffs' in params and params['poly_coeffs'] is not None:
            self.poly_coeffs = params['poly_coeffs'].copy()

        # Update scaler parameters
        if 'scaler_X_params' in params:
            scaler_params = params['scaler_X_params']
            if scaler_params['mean_'] is not None:
                self.scaler_X.mean_ = scaler_params['mean_'].copy()
            if scaler_params['scale_'] is not None:
                self.scaler_X.scale_ = scaler_params['scale_'].copy()

        if 'scaler_y_params' in params:
            scaler_params = params['scaler_y_params']
            if scaler_params['mean_'] is not None:
                self.scaler_y.mean_ = scaler_params['mean_'].copy()
            if scaler_params['scale_'] is not None:
                self.scaler_y.scale_ = scaler_params['scale_'].copy()

        # Mark as fitted if we have essential parameters
        if self.weights is not None and self.poly_coeffs is not None:
            self.is_fitted = True


class PRSModel(SurrogateModel):
    """Polynomial Response Surface Model (second-order)"""
    
    def __init__(self):
        """Initialize PRS model"""
        self.coeffs = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        self.is_fitted = False
        
    def _build_polynomial_matrix(self, X: np.ndarray) -> np.ndarray:
        """Build second-order polynomial matrix"""
        n, d = X.shape
        # Number of terms: 1 + d + d*(d+1)/2
        n_terms = 1 + d + d * (d + 1) // 2
        P = np.zeros((n, n_terms))
        
        idx = 0
        # Constant term
        P[:, idx] = 1
        idx += 1
        
        # Linear terms
        P[:, idx:idx+d] = X
        idx += d
        
        # Quadratic terms
        for i in range(d):
            for j in range(i, d):
                P[:, idx] = X[:, i] * X[:, j]
                idx += 1
                
        return P
        
    def fit(self, X: np.ndarray, y: np.ndarray):
        """Train the PRS model"""
        if len(X) == 0:
            raise ValueError("Cannot fit PRS model with empty data")
            
        X_scaled = self.scaler_X.fit_transform(X)
        y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).ravel()
        
        P = self._build_polynomial_matrix(X_scaled)
        
        # Solve using least squares
        try:
            self.coeffs = np.linalg.lstsq(P, y_scaled, rcond=None)[0]
        except np.linalg.LinAlgError:
            self.coeffs = np.linalg.pinv(P) @ y_scaled
            
        self.is_fitted = True
            
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Predict using PRS model"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
            
        X_scaled = self.scaler_X.transform(X)
        P = self._build_polynomial_matrix(X_scaled)
        y_pred_scaled = P @ self.coeffs
        y_pred = self.scaler_y.inverse_transform(y_pred_scaled.reshape(-1, 1)).ravel()

        return y_pred

    def get_parameters(self) -> Dict:
        """Extract PRS model parameters for federated learning"""
        if not self.is_fitted:
            return {}

        params = {
            'coeffs': self.coeffs.copy() if self.coeffs is not None else None,
            'scaler_X_params': {
                'mean_': self.scaler_X.mean_.copy() if hasattr(self.scaler_X, 'mean_') else None,
                'scale_': self.scaler_X.scale_.copy() if hasattr(self.scaler_X, 'scale_') else None
            },
            'scaler_y_params': {
                'mean_': self.scaler_y.mean_.copy() if hasattr(self.scaler_y, 'mean_') else None,
                'scale_': self.scaler_y.scale_.copy() if hasattr(self.scaler_y, 'scale_') else None
            }
        }

        return params

    def set_parameters(self, params: Dict):
        """Set PRS model parameters from federated aggregation"""
        if not params:
            return

        # Update coefficients
        if 'coeffs' in params and params['coeffs'] is not None:
            self.coeffs = params['coeffs'].copy()

        # Update scaler parameters
        if 'scaler_X_params' in params:
            scaler_params = params['scaler_X_params']
            if scaler_params['mean_'] is not None:
                self.scaler_X.mean_ = scaler_params['mean_'].copy()
            if scaler_params['scale_'] is not None:
                self.scaler_X.scale_ = scaler_params['scale_'].copy()

        if 'scaler_y_params' in params:
            scaler_params = params['scaler_y_params']
            if scaler_params['mean_'] is not None:
                self.scaler_y.mean_ = scaler_params['mean_'].copy()
            if scaler_params['scale_'] is not None:
                self.scaler_y.scale_ = scaler_params['scale_'].copy()

        # Mark as fitted if we have coefficients
        if self.coeffs is not None:
            self.is_fitted = True


class KNNModel(SurrogateModel):
    """K-Nearest Neighbors Model for classification-based optimization"""
    
    def __init__(self, k: int = 1, n_levels: int = 5):
        """
        Initialize KNN model
        
        Args:
            k: Number of neighbors
            n_levels: Number of fitness levels for classification
        """
        self.k = k
        self.n_levels = n_levels
        self.model = KNeighborsClassifier(n_neighbors=k)
        self.scaler_X = StandardScaler()
        self.y_train = None
        self.is_fitted = False
        
    def fit(self, X: np.ndarray, y: np.ndarray):
        """Train the KNN model"""
        if len(X) == 0:
            raise ValueError("Cannot fit KNN model with empty data")
            
        X_scaled = self.scaler_X.fit_transform(X)
        self.y_train = y.copy()
        
        # Divide population into levels based on fitness
        n = len(y)
        sorted_indices = np.argsort(y)
        levels = np.zeros(n, dtype=int)
        
        level_size = n // self.n_levels
        for i in range(self.n_levels):
            start_idx = i * level_size
            end_idx = (i + 1) * level_size if i < self.n_levels - 1 else n
            levels[sorted_indices[start_idx:end_idx]] = i + 1
            
        self.model.fit(X_scaled, levels)
        self.is_fitted = True
        
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Predict fitness levels"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
            
        X_scaled = self.scaler_X.transform(X)
        return self.model.predict(X_scaled)

    def get_parameters(self) -> Dict:
        """Extract KNN model parameters for federated learning"""
        if not self.is_fitted:
            return {}

        params = {
            'k': self.k,
            'n_levels': self.n_levels,
            'scaler_X_params': {
                'mean_': self.scaler_X.mean_.copy() if hasattr(self.scaler_X, 'mean_') else None,
                'scale_': self.scaler_X.scale_.copy() if hasattr(self.scaler_X, 'scale_') else None
            },
            # Note: We don't share the actual training data for privacy
            'y_train_stats': {
                'min': np.min(self.y_train) if self.y_train is not None else None,
                'max': np.max(self.y_train) if self.y_train is not None else None,
                'mean': np.mean(self.y_train) if self.y_train is not None else None,
                'std': np.std(self.y_train) if self.y_train is not None else None
            }
        }

        return params

    def set_parameters(self, params: Dict):
        """Set KNN model parameters from federated aggregation"""
        if not params:
            return

        # Update hyperparameters
        if 'k' in params:
            self.k = params['k']
            self.model.n_neighbors = self.k

        if 'n_levels' in params:
            self.n_levels = params['n_levels']

        # Update scaler parameters
        if 'scaler_X_params' in params:
            scaler_params = params['scaler_X_params']
            if scaler_params['mean_'] is not None:
                self.scaler_X.mean_ = scaler_params['mean_'].copy()
            if scaler_params['scale_'] is not None:
                self.scaler_X.scale_ = scaler_params['scale_'].copy()

        # Note: For KNN, we can't directly transfer the model without data
        # The model needs to be retrained locally with updated hyperparameters


class InfillCriterion(ABC):
    """Abstract base class for infill criteria"""

    @abstractmethod
    def select_solution(self, model: SurrogateModel, candidates: np.ndarray,
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        """Select the best candidate solution"""
        pass


class LCBCriterion(InfillCriterion):
    """Lower Confidence Bound criterion for GP models"""

    def __init__(self, w: float = 2.0):
        """
        Initialize LCB criterion

        Args:
            w: Weight parameter for exploration-exploitation balance
        """
        self.w = w

    def select_solution(self, model: GPModel, candidates: np.ndarray,
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        """Select solution with minimum LCB value"""
        y_pred, std = model.predict(candidates)
        lcb_values = y_pred - self.w * std
        return np.argmin(lcb_values)


class EICriterion(InfillCriterion):
    """Expected Improvement criterion for GP models"""

    def select_solution(self, model: GPModel, candidates: np.ndarray,
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        """Select solution with maximum EI value"""
        y_pred, std = model.predict(candidates)

        # Avoid division by zero
        std = np.maximum(std, 1e-9)

        z = (f_min - y_pred) / std
        ei_values = (f_min - y_pred) * norm.cdf(z) + std * norm.pdf(z)
        return np.argmax(ei_values)


class PrescreeningCriterion(InfillCriterion):
    """Prescreening criterion for RBF/PRS models"""

    def select_solution(self, model: SurrogateModel, candidates: np.ndarray,
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        """Select solution with minimum predicted value"""
        y_pred = model.predict(candidates)
        return np.argmin(y_pred)


class LocalSearchCriterion(InfillCriterion):
    """Local search criterion for RBF/PRS models"""

    def __init__(self, max_iter: int = 1000):
        """
        Initialize local search criterion

        Args:
            max_iter: Maximum iterations for local optimization
        """
        self.max_iter = max_iter

    def select_solution(self, model: SurrogateModel, candidates: np.ndarray,
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        """Select solution using local optimization"""
        # Define local search bounds based on current population
        lb = np.min(population, axis=0)
        ub = np.max(population, axis=0)

        # Ensure bounds are within global bounds
        lb = np.maximum(lb, bounds[:, 0])
        ub = np.minimum(ub, bounds[:, 1])

        def objective(x):
            return model.predict(x.reshape(1, -1))[0]

        # Start from the best candidate
        best_candidate_idx = np.argmin(model.predict(candidates))
        x0 = candidates[best_candidate_idx]

        # Perform local optimization
        try:
            result = minimize(objective, x0, bounds=list(zip(lb, ub)),
                             method='L-BFGS-B', options={'maxiter': self.max_iter})

            if result.success:
                # Return the index of the closest candidate to the optimum
                distances = np.linalg.norm(candidates - result.x, axis=1)
                return np.argmin(distances)
        except:
            pass

        return best_candidate_idx


class L1ExploitationCriterion(InfillCriterion):
    """L1-Exploitation criterion for KNN model"""

    def select_solution(self, model: KNNModel, candidates: np.ndarray,
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        """Select L1 candidate that maximizes minimum distance to L1 population"""
        # Predict levels for candidates
        levels = model.predict(candidates)

        # Find L1 (best level) candidates
        l1_indices = np.where(levels == 1)[0]
        if len(l1_indices) == 0:
            # If no L1 candidates, return the one with best predicted level
            return np.argmin(levels)

        l1_candidates = candidates[l1_indices]

        # Find L1 solutions in current population
        y_train = model.y_train
        n = len(y_train)
        sorted_indices = np.argsort(y_train)
        level_size = n // model.n_levels
        l1_pop_indices = sorted_indices[:level_size]
        l1_population = population[l1_pop_indices]

        # Find the L1 candidate that maximizes minimum distance to L1 population
        max_min_dist = -1
        best_idx = 0

        for i, candidate in enumerate(l1_candidates):
            distances = np.linalg.norm(l1_population - candidate, axis=1)
            min_dist = np.min(distances)
            if min_dist > max_min_dist:
                max_min_dist = min_dist
                best_idx = i

        return l1_indices[best_idx]


class L1ExplorationCriterion(InfillCriterion):
    """L1-Exploration criterion for KNN model"""

    def select_solution(self, model: KNNModel, candidates: np.ndarray,
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        """Select L1 candidate that minimizes maximum distance to L1 population"""
        # Predict levels for candidates
        levels = model.predict(candidates)

        # Find L1 (best level) candidates
        l1_indices = np.where(levels == 1)[0]
        if len(l1_indices) == 0:
            # If no L1 candidates, return the one with best predicted level
            return np.argmin(levels)

        l1_candidates = candidates[l1_indices]

        # Find L1 solutions in current population
        y_train = model.y_train
        n = len(y_train)
        sorted_indices = np.argsort(y_train)
        level_size = n // model.n_levels
        l1_pop_indices = sorted_indices[:level_size]
        l1_population = population[l1_pop_indices]

        # Find the L1 candidate that minimizes maximum distance to L1 population
        min_max_dist = float('inf')
        best_idx = 0

        for i, candidate in enumerate(l1_candidates):
            distances = np.linalg.norm(l1_population - candidate, axis=1)
            max_dist = np.max(distances)
            if max_dist < min_max_dist:
                min_max_dist = max_dist
                best_idx = i

        return l1_indices[best_idx]


class DEOperator:
    """Differential Evolution Operator for candidate generation"""

    def __init__(self, F: float = 0.5, CR: float = 0.9):
        """
        Initialize DE operator

        Args:
            F: Differential weight
            CR: Crossover probability
        """
        self.F = F
        self.CR = CR

    def generate_offspring(self, population: np.ndarray, bounds: np.ndarray) -> np.ndarray:
        """Generate offspring using DE/best/1 mutation and binomial crossover"""
        n, d = population.shape
        offspring = np.zeros_like(population)

        for i in range(n):
            # Select three random individuals (different from current)
            candidates = list(range(n))
            candidates.remove(i)

            if len(candidates) < 3:
                # If not enough candidates, use random selection with replacement
                r1, r2, r3 = np.random.choice(candidates, 3, replace=True)
            else:
                r1, r2, r3 = np.random.choice(candidates, 3, replace=False)

            # Find best individual (assuming population is sorted)
            best_idx = 0

            # DE/best/1 mutation
            mutant = population[i] + self.F * (population[best_idx] - population[i]) + \
                    self.F * (population[r1] - population[r2])

            # Binomial crossover
            j_rand = np.random.randint(d)
            for j in range(d):
                if np.random.random() <= self.CR or j == j_rand:
                    offspring[i, j] = mutant[j]
                else:
                    offspring[i, j] = population[i, j]

            # Boundary handling
            offspring[i] = np.clip(offspring[i], bounds[:, 0], bounds[:, 1])

        return offspring


class TwoLevelMAB:
    """Two-Level Multi-Armed Bandit for model and criterion selection"""

    def __init__(self, alpha: float = 2.0):
        """
        Initialize TL-MAB

        Args:
            alpha: UCB parameter for exploration-exploitation balance
        """
        self.alpha = alpha

        # High-level arms (models)
        self.high_arms = ['GP', 'RBF', 'PRS', 'KNN']

        # Low-level arms (criteria) and their associations
        self.low_arms = ['LCB', 'EI', 'prescreening_rbf', 'localsearch_rbf',
                        'prescreening_prs', 'localsearch_prs', 'L1_exploitation', 'L1_exploration']

        # Association between high and low level arms
        self.associations = {
            'GP': ['LCB', 'EI'],
            'RBF': ['prescreening_rbf', 'localsearch_rbf'],
            'PRS': ['prescreening_prs', 'localsearch_prs'],
            'KNN': ['L1_exploitation', 'L1_exploration']
        }

        # Initialize values and selection counts
        self.high_values = {arm: 0.0 for arm in self.high_arms}
        self.low_values = {arm: 0.0 for arm in self.low_arms}
        self.high_counts = {arm: 0 for arm in self.high_arms}
        self.low_counts = {arm: 0 for arm in self.low_arms}

        # Combinatorial arms for initial exploration
        self.combinatorial_arms = [
            ('GP', 'LCB'), ('GP', 'EI'),
            ('RBF', 'prescreening_rbf'), ('RBF', 'localsearch_rbf'),
            ('PRS', 'prescreening_prs'), ('PRS', 'localsearch_prs'),
            ('KNN', 'L1_exploitation'), ('KNN', 'L1_exploration')
        ]

    def select_arms(self, t: int) -> Tuple[str, str]:
        """Select high-level and low-level arms using TL-UCB"""

        # Initial exploration phase
        if t <= len(self.combinatorial_arms):
            return self.combinatorial_arms[t-1]

        # TL-UCB selection
        # Select high-level arm
        high_ucb_values = {}
        for arm in self.high_arms:
            if self.high_counts[arm] == 0:
                high_ucb_values[arm] = float('inf')
            else:
                confidence = np.sqrt(self.alpha * np.log(t) / self.high_counts[arm])
                high_ucb_values[arm] = self.high_values[arm] + confidence

        selected_high = max(high_ucb_values, key=high_ucb_values.get)

        # Select low-level arm from associated arms
        associated_low_arms = self.associations[selected_high]
        low_ucb_values = {}

        for arm in associated_low_arms:
            if self.low_counts[arm] == 0:
                low_ucb_values[arm] = float('inf')
            else:
                confidence = np.sqrt(self.alpha * np.log(t) / self.low_counts[arm])
                low_ucb_values[arm] = self.low_values[arm] + confidence

        selected_low = max(low_ucb_values, key=low_ucb_values.get)

        return selected_high, selected_low

    def update_rewards(self, high_arm: str, low_arm: str, population: np.ndarray,
                      new_solution: np.ndarray, new_fitness: float):
        """Update rewards using TL-R mechanism"""

        # Calculate ranking of new solution
        population_fitness = population[:, -1] if population.shape[1] > len(new_solution) else []
        if len(population_fitness) > 0:
            ranking = np.sum(population_fitness < new_fitness) + 1
            n = len(population_fitness)
        else:
            ranking = 1
            n = 1

        # Low-level reward (Equation 24 in paper)
        low_reward = -1/n * ranking + (n+1)/n

        # Update low-level arm value (Equation 25 in paper)
        old_low_value = self.low_values[low_arm]
        self.low_counts[low_arm] += 1
        self.low_values[low_arm] = (self.low_counts[low_arm] - 1) * old_low_value + low_reward
        self.low_values[low_arm] /= self.low_counts[low_arm]

        # High-level reward (Equation 26 in paper)
        value_change = self.low_values[low_arm] - old_low_value
        n_associated = len(self.associations[high_arm])
        high_reward = value_change / n_associated

        # Update high-level arm value (Equation 27 in paper)
        self.high_counts[high_arm] += 1
        self.high_values[high_arm] += high_reward

    def get_state(self) -> Dict:
        """Get current MAB state for serialization"""
        return {
            'high_values': self.high_values.copy(),
            'low_values': self.low_values.copy(),
            'high_counts': self.high_counts.copy(),
            'low_counts': self.low_counts.copy()
        }

    def set_state(self, state: Dict):
        """Set MAB state from deserialized data"""
        self.high_values = state['high_values']
        self.low_values = state['low_values']
        self.high_counts = state['high_counts']
        self.low_counts = state['low_counts']


def latin_hypercube_sampling(n_samples: int, dimension: int, bounds: np.ndarray) -> np.ndarray:
    """Generate initial population using Latin Hypercube Sampling"""
    samples = np.random.random((n_samples, dimension))

    for i in range(dimension):
        # Generate n_samples equally spaced intervals
        intervals = np.linspace(0, 1, n_samples + 1)
        # Randomly permute the intervals
        perm = np.random.permutation(n_samples)
        # Sample within each interval
        for j in range(n_samples):
            low = intervals[perm[j]]
            high = intervals[perm[j] + 1]
            samples[j, i] = low + (high - low) * samples[j, i]

    # Scale to actual bounds
    for i in range(dimension):
        samples[:, i] = bounds[i, 0] + samples[:, i] * (bounds[i, 1] - bounds[i, 0])

    return samples


def create_model_criterion_mapping():
    """Create mapping between models and their associated criteria"""
    return {
        'GP': {
            'model_class': GPModel,
            'criteria': {
                'LCB': LCBCriterion,
                'EI': EICriterion
            }
        },
        'RBF': {
            'model_class': RBFModel,
            'criteria': {
                'prescreening_rbf': PrescreeningCriterion,
                'localsearch_rbf': LocalSearchCriterion
            }
        },
        'PRS': {
            'model_class': PRSModel,
            'criteria': {
                'prescreening_prs': PrescreeningCriterion,
                'localsearch_prs': LocalSearchCriterion
            }
        },
        'KNN': {
            'model_class': KNNModel,
            'criteria': {
                'L1_exploitation': L1ExploitationCriterion,
                'L1_exploration': L1ExplorationCriterion
            }
        }
    }


def aggregate_mab_experiences(experiences: List[Dict]) -> Dict:
    """Aggregate MAB experiences from multiple clients"""
    if not experiences:
        return {}

    # Initialize aggregated state
    aggregated = {
        'high_values': {},
        'low_values': {},
        'high_counts': {},
        'low_counts': {}
    }

    # Get all arms from first experience
    first_exp = experiences[0]
    high_arms = list(first_exp['high_values'].keys())
    low_arms = list(first_exp['low_values'].keys())

    # Initialize with zeros
    for arm in high_arms:
        aggregated['high_values'][arm] = 0.0
        aggregated['high_counts'][arm] = 0

    for arm in low_arms:
        aggregated['low_values'][arm] = 0.0
        aggregated['low_counts'][arm] = 0

    # Aggregate counts and weighted values
    for exp in experiences:
        for arm in high_arms:
            count = exp['high_counts'][arm]
            value = exp['high_values'][arm]
            aggregated['high_counts'][arm] += count
            aggregated['high_values'][arm] += count * value

        for arm in low_arms:
            count = exp['low_counts'][arm]
            value = exp['low_values'][arm]
            aggregated['low_counts'][arm] += count
            aggregated['low_values'][arm] += count * value

    # Calculate weighted averages
    for arm in high_arms:
        if aggregated['high_counts'][arm] > 0:
            aggregated['high_values'][arm] /= aggregated['high_counts'][arm]

    for arm in low_arms:
        if aggregated['low_counts'][arm] > 0:
            aggregated['low_values'][arm] /= aggregated['low_counts'][arm]

    return aggregated


def aggregate_model_parameters(model_params_list: List[Dict], weights: Optional[List[float]] = None) -> Dict:
    """
    Aggregate model parameters from multiple clients using weighted averaging

    Args:
        model_params_list: List of model parameter dictionaries from clients
        weights: Optional weights for each client (based on performance)

    Returns:
        Aggregated model parameters
    """
    if not model_params_list:
        return {}

    # Use uniform weights if not provided
    if weights is None:
        weights = [1.0 / len(model_params_list)] * len(model_params_list)
    else:
        # Normalize weights
        total_weight = sum(weights)
        weights = [w / total_weight for w in weights]

    aggregated_params = {}

    # Get all model types from first client
    model_types = list(model_params_list[0].keys())

    for model_type in model_types:
        aggregated_params[model_type] = {}

        # Collect parameters for this model type from all clients
        client_params = []
        client_weights = []

        for i, client_params_dict in enumerate(model_params_list):
            if model_type in client_params_dict and client_params_dict[model_type]:
                client_params.append(client_params_dict[model_type])
                client_weights.append(weights[i])

        if not client_params:
            continue

        # Normalize weights for this model type
        total_weight = sum(client_weights)
        if total_weight > 0:
            client_weights = [w / total_weight for w in client_weights]

        # Aggregate parameters based on model type
        if model_type == 'GP':
            aggregated_params[model_type] = _aggregate_gp_parameters(client_params, client_weights)
        elif model_type == 'RBF':
            aggregated_params[model_type] = _aggregate_rbf_parameters(client_params, client_weights)
        elif model_type == 'PRS':
            aggregated_params[model_type] = _aggregate_prs_parameters(client_params, client_weights)
        elif model_type == 'KNN':
            aggregated_params[model_type] = _aggregate_knn_parameters(client_params, client_weights)

    return aggregated_params


def _aggregate_gp_parameters(client_params: List[Dict], weights: List[float]) -> Dict:
    """Aggregate GP model parameters"""
    aggregated = {}

    # Aggregate kernel theta (hyperparameters)
    if all('kernel_theta' in params for params in client_params):
        theta_arrays = [params['kernel_theta'] for params in client_params]
        aggregated['kernel_theta'] = np.average(theta_arrays, axis=0, weights=weights)

    # Aggregate alpha (noise parameter)
    if all('alpha' in params for params in client_params):
        alpha_values = [params['alpha'] for params in client_params]
        aggregated['alpha'] = np.average(alpha_values, weights=weights)

    # Keep other parameters from majority vote or first client
    for key in ['normalize_y', 'n_restarts_optimizer']:
        if all(key in params for params in client_params):
            aggregated[key] = client_params[0][key]  # Use first client's value

    return aggregated


def _aggregate_rbf_parameters(client_params: List[Dict], weights: List[float]) -> Dict:
    """Aggregate RBF model parameters"""
    aggregated = {}

    # Aggregate weights (if same size)
    weight_arrays = [params.get('weights') for params in client_params if params.get('weights') is not None]
    if weight_arrays and all(w.shape == weight_arrays[0].shape for w in weight_arrays):
        aggregated['weights'] = np.average(weight_arrays, axis=0, weights=weights[:len(weight_arrays)])

    # Aggregate polynomial coefficients
    poly_arrays = [params.get('poly_coeffs') for params in client_params if params.get('poly_coeffs') is not None]
    if poly_arrays and all(p.shape == poly_arrays[0].shape for p in poly_arrays):
        aggregated['poly_coeffs'] = np.average(poly_arrays, axis=0, weights=weights[:len(poly_arrays)])

    # Aggregate scaler parameters
    aggregated['scaler_X_params'] = _aggregate_scaler_params(
        [params.get('scaler_X_params', {}) for params in client_params], weights
    )
    aggregated['scaler_y_params'] = _aggregate_scaler_params(
        [params.get('scaler_y_params', {}) for params in client_params], weights
    )

    return aggregated


def _aggregate_prs_parameters(client_params: List[Dict], weights: List[float]) -> Dict:
    """Aggregate PRS model parameters"""
    aggregated = {}

    # Aggregate coefficients
    coeff_arrays = [params.get('coeffs') for params in client_params if params.get('coeffs') is not None]
    if coeff_arrays and all(c.shape == coeff_arrays[0].shape for c in coeff_arrays):
        aggregated['coeffs'] = np.average(coeff_arrays, axis=0, weights=weights[:len(coeff_arrays)])

    # Aggregate scaler parameters
    aggregated['scaler_X_params'] = _aggregate_scaler_params(
        [params.get('scaler_X_params', {}) for params in client_params], weights
    )
    aggregated['scaler_y_params'] = _aggregate_scaler_params(
        [params.get('scaler_y_params', {}) for params in client_params], weights
    )

    return aggregated


def _aggregate_knn_parameters(client_params: List[Dict], weights: List[float]) -> Dict:
    """Aggregate KNN model parameters"""
    aggregated = {}

    # Aggregate hyperparameters using weighted mode/average
    k_values = [params.get('k') for params in client_params if params.get('k') is not None]
    if k_values:
        # Use weighted average and round to nearest integer
        aggregated['k'] = int(np.round(np.average(k_values, weights=weights[:len(k_values)])))

    n_levels_values = [params.get('n_levels') for params in client_params if params.get('n_levels') is not None]
    if n_levels_values:
        aggregated['n_levels'] = int(np.round(np.average(n_levels_values, weights=weights[:len(n_levels_values)])))

    # Aggregate scaler parameters
    aggregated['scaler_X_params'] = _aggregate_scaler_params(
        [params.get('scaler_X_params', {}) for params in client_params], weights
    )

    return aggregated


def _aggregate_scaler_params(scaler_params_list: List[Dict], weights: List[float]) -> Dict:
    """Aggregate scaler parameters"""
    aggregated = {}

    # Aggregate mean
    means = [params.get('mean_') for params in scaler_params_list if params.get('mean_') is not None]
    if means and all(m.shape == means[0].shape for m in means):
        aggregated['mean_'] = np.average(means, axis=0, weights=weights[:len(means)])

    # Aggregate scale
    scales = [params.get('scale_') for params in scaler_params_list if params.get('scale_') is not None]
    if scales and all(s.shape == scales[0].shape for s in scales):
        aggregated['scale_'] = np.average(scales, axis=0, weights=weights[:len(scales)])

    return aggregated
