{"version": "", "pages": [{"url": "", "pageIdx": 0, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: 1114 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->\n\n# Surrogate-Assisted Evolutionary Algorithm With Model and Infill Criterion Auto-Configuration\n\nLindong Xie \\( {}^{\\circledR } \\) , <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> \\( {}^{\\circledR } \\) , Member, IEEE, <PERSON>z<PERSON> \\( {}^{\\circledR } \\) , Senior Member, IEEE, and Maoguo Gong \\( {}^{ \\oplus  } \\) ,Senior Member, IEEE\n\nAbstract-Surrogate-assisted evolutionary algorithms (SAEAs) have proven to be effective in solving computationally expensive optimization problems (EOPs). However, the performance of SAEAs heavily relies on the surrogate model and infill criterion used. To improve the generalization of SAEAs and enable them to solve a wide range of EOPs, this article proposes an SAEA called AutoSAEA, which features model and infill criterion auto-configuration. Specifically, AutoSAEA formulates model and infill criterion selection as a two-level multiarmed bandit problem (TL-MAB). The first and second levels cooperate in selecting the surrogate model and infill criterion, respectively. A two-level reward (TL-R) measures the value of the surrogate model and infill criterion, while a two-level upper confidence bound (TL-UCB) selects the model and infill criterion in an online manner. Numerous experiments validate the superiority of AutoSAEA over some state-of-the-art SAEAs on complex benchmark problems and a real-world oil reservoir production optimization problem.\n\nIndex Terms-Auto algorithm design, expensive optimization, surrogate-assisted evolutionary algorithm (SAEA), two-level multiarmed bandit (TL-MAB).\n\n## I. INTRODUCTION\n\nID XPENSIVE optimization problems (EOPs) are prevalent in many engineering designs and applications (e.g., aerodynamic structures design [1], automobile crash analysis [2], and space tethered-net system design [3]). Unfortunately, solving such problems requires conducting computationally expensive computer simulations or costly physics experiments since the analytical expressions of the problem are unavailable. Generally, the EOP can be expressed as follows:\n\n\\[\\min f\\left( \\mathbf{x}\\right) \\]\n\n\\[\\text{s.t.}{\\mathbf{x}}_{l} \\leq  \\mathbf{x} \\leq  {\\mathbf{x}}_{u} \\tag{1}\\]\n\nwhere \\( \\mathbf{x} = {\\left( {x}_{1},\\ldots ,{x}_{D}\\right) }^{\\top } \\) is the decision vector of \\( D \\) variables, \\( {\\mathbf{x}}_{l} = {\\left( {x}_{l,1},\\ldots ,{x}_{l,D}\\right) }^{\\top } \\) and \\( {\\mathbf{x}}_{u} = {\\left( {x}_{u,1},\\ldots ,{x}_{u,D}\\right) }^{\\top } \\) are the lower and upper bounds of the search space,respectively,and \\( f\\left( \\mathbf{x}\\right) \\) denotes a scalar objective function. We assume that the analytical expression of \\( f\\left( \\mathbf{x}\\right) \\) is unavailable and the calculation of \\( f\\left( \\mathbf{x}\\right) \\) is costly.\n\nEvolutionary algorithms have been shown to be popular and effective for solving black-box optimization problems [4], [5]. However, they often perform poorly on EOPs due to the large number of function evaluations (FEs) required in their optimization process. This limitation becomes more significant in the case of EOPs [6]. To address this issue, surrogate-assisted evolutionary algorithms (SAEAs) have been proposed. In SAEAs, the optimization is mainly driven by computationally cheap surrogate models. The popular surrogate models used in SAEAs include regression models, such as radial basis function (RBF) [7], Gaussian process model (GP) [8], and polynomial response surface (PRS) [9],and classification models,such as \\( k \\) -nearest neighbor (KNN) [10] and support vector machine model (SVM) [11]. Based on the adopted surrogate model, existing SAEAs can be roughly grouped into three categories: 1) single-model SAEAs [12], [13], [14], [15]; 2) multiple-model SAEAs [16], [17], [18], [19], [20], [21]; and 3) adaptive-model SAEAs [22], [23], [24], [25].\n\nSingle-model SAEAs use a fixed surrogate model throughout the optimization process, typically combining a fixed infill criterion, such as GP with expected improvement (EI) [26] and lower confidence bound (LCB) [27], RBF with local search [28], or KNN with prescreening [29], to select candidate solutions for FEs. However, due to the limited training samples available and the inability of a single model to effectively fit various problem landscapes, single-model SAEAs often struggle to solve different EOPs effectively [18], [30].\n\nIn order to enhance the robustness and scalability of single-model SAEAs, multiple-model SAEAs have been developed, including hierarchical-model SAEAs and ensemble-model SAEAs [31]. Hierarchical-model SAEAs commonly combine a global model and a local model [16], [18], [32], [33], [34]. The global model approximates the fitness landscape to search for promising regions, and the local model is used to conduct a refined search in the found local promising regions. Ensemble-model SAEAs typically train several different base surrogate models to cooperatively implement a more precise prediction [9], [19], [20], [21]. Generally, multiple-model SAEAs outperform single-model ones in most cases [15]. However, the computational cost of multiple-model SAEAs is always higher compared to the single-model SAEAs [35], [36]. Additionally, some base models may not fit the problem landscape well, so their efficiency may still be low.\n\n---\n\n<!-- Footnote -->\n\nManuscript received 28 January 2023; revised 7 May 2023; accepted 27 June 2023. Date of publication 3 July 2023; date of current version 1 August 2024. This work was supported in part by the National Natural Science Foundation of China under Grant 62206120, Grant 62106096, and Grant 62036006; and in part by the Shenzhen Technology Plan under Grant JCYJ20220530113013031. (Lindong Xie and Genghui Li contributed equally to this work.) (Corresponding author: Zhenkun Wang.)\n\nLindong Xie and Genghui Li are with the School of System Design and Intelligent Manufacturing, Southern University of Science and Technology, Shenzhen 518055, China (e-mail: <EMAIL>; <EMAIL>).\n\nZhenkun Wang is with the School of System Design and Intelligent Manufacturing and the Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China (e-mail: <EMAIL>).\n\nLaizhong Cui is with the College of Computer Science and Software Engineering and the Guangdong Laboratory of Artificial Intelligence and Digital Economy (SZ), Shenzhen University, Shenzhen 518060, China (e-mail: <EMAIL>).\n\nMaoguo Gong is with the Key Laboratory of Collaborative Intelligence Systems, Ministry of Education, Xidian University, Xi'an 710071, China (e-mail: <EMAIL>).\n\nThis article has supplementary material provided by the authors and color versions of one or more figures available at https://doi.org/10.1109/ TEVC.2023.3291614.\n\nDigital Object Identifier 10.1109/TEVC.2023.3291614\n\n<!-- Footnote -->\n\n---\n\n<!-- Meanless: 1089-778X (C) 2023 IEEE. Personal use is permitted, but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information. Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 1, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: XIE et al.: SAEA 1115-->\n\nTo leverage well-established models effectively and reduce computational complexity, adaptive-model SAEAs have been proposed that automatically select the suitable model (or infill criterion) in an online manner [22], [24], [25]. However, the performance of such algorithms dramatically depends on the design of the adaptive selection strategy. It is well known that the surrogate model is tightly coupled with the infill criterion in SAEAs, and they cooperatively affect the performance of SAEAs. A model with different infill criteria can realize different balances of exploiting better solutions and exploring unexplored search regions. Naturally, a better balance between exploration and exploitation may be achieved by cooperatively considering the model and the infill criterion in a hierarchical coupled way. Unfortunately, existing adaptive-model SAEAs are either only for model or infill criterion selection but do not study adaptive selection for both cooperatively. Therefore, to improve the performance of SAEAs by filling this gap, this article proposes an SAEA (called AutoSAEA) with the model and infill criterion auto-configuration by using the hierarchical multiarmed bandit (MAB) method.\n\nThe contributions of this article are summarized as follows.\n\n1) Formulating the surrogate model and infill criterion cooperative selection as a two-level MAB problem (TL-MAB) [37], [38], [39].\n\n2) Designing a two-level reward (TL-R) to measure the utility of the surrogate model and infill criterion in a cooperative manner.\n\n3) Adopting a two-level upper confidence bound (TL-UCB) to select the surrogate model and infill criterion cooperatively in an online manner.\n\n4) Proposing an SAEA with a surrogate model and infill criterion auto-configuration, and verifying its advantages by comparing it with some state-of-the-art SAEAs on two sets of complex benchmark problems and a real-world problem.\n\nThe remainder of this article is organized as follows. Section II reviews the related work. Section III introduces the background knowledge involved in the proposed algorithm. Section IV describes the proposed algorithm in detail. Section V presents the numerical experiments conducted to validate the effectiveness of the proposed algorithm. Finally, Section VI summarizes this article and discusses future work directions.\n\n## II. RELATED WORK\n\n1) Single-Model SAEAs: Single-model SAEAs can either train a global model using all evaluated solutions or a local model with some good ones to predict the quality of new solutions. For example, the Bayesian optimization framework is a classic and popular single-model method [40], [41], [42]. It uses the GP model to build a surrogate for the objective and quantify the uncertainty in the surrogate. Then, it optimizes an acquisition function (e.g., EI [40] and LCB [43]) defined from the surrogate to decide the new solution for evaluation. GPEME [13] first constructs a local GP model in the lower-dimensional space, followed by the application of prescreening with LCB to determine which offspring can be selected for FE. CPS-MOEA [44] employs a local KNN classifier to filter out potentially nondominated solutions for real evaluation. SA-COSO [45] evaluates the new position of the particle that has the minimum fitness value predicted by a global RBF model. In addition, SHPSO [46] and SAMSO [15] construct a local RBF model and a global RBF model, respectively, to select particles with predicted fitness values that are better than their personal best ones for FEs. MGP-SLPSO [14] formulates the approximated fitness and its corresponding uncertainty provided by a global GP model as a bi-objective problem and applies a nondominated sorting method to select the offspring for FEs. Furthermore, CA-LLSO [47] trains a local gradient boosting classifier to predict the level of the offspring produced by the level-based learning swarm optimizer.\n\n2) Multiple-Model SAEAs: Multiple-model SAEAs mainly combine a global and a local model or train multiple models simultaneously to balance exploration and exploitation. For example, CAL-SAPSO [19] identifies the best and most uncertain solutions for FEs using a global ensemble surrogate model. Additionally, the optimum of the local ensemble model found by the local search is also used for expensive evaluation. HeE-MOEA [35] utilizes an SVM and two RBF models to build an ensemble model, which is combined with LCB and EI criteria to screen the offspring for expensive evaluations. ESAO [32] employs a global RBF to screen the offspring with the minimum predicted fitness value for FE. Besides, the optimum of the local RBF model found by the differential evolution (DE) is also evaluated. GSGA [33] adopts a local RBF-assisted trust-region method and a global GP-assisted genetic algorithm for exploitation and exploration, respectively. GL-SADE [18] trains a global RBF model and a local GP model to select offspring for FEs. Moreover, when the local GP model finds the current best solution, DE is applied further to search for its optimal solution. ESCO [21] constructs multiple RBF models on various low-dimensional sample sets, and then selects the models with superior performance to compose an ensemble surrogate.\n\n3) Adaptive-Model SAEAs: Adaptive-model SAEAs always design an adaptive selection strategy to automatically choose a model or an infill criterion in an online manner. For example, GP-Hedge [22] adaptively selects an appropriate acquisition function from a portfolio of well-established ones, such as the probability of improvement, EI, and UCB. This selection is based on an online MAB strategy. Specifically, the optimal solutions of all acquisition functions form a candidate solution pool, from which solutions are selected for FEs based on the cumulative rewards of their respective acquisition functions. While GP-Hedge only adaptively selects the acquisition functions, it fixes the surrogate model. Moreover, its computational efficiency is low since it needs to optimize multiple acquisition functions in each generation. ASMEA [30] first constructs a base model pool using GP, RBF, PRS, and linear Shepard interpolation. It then uses the prediction residual error sum of squares (PRESS) and root mean square error (RMSE) to select several meta-models from the base model pool for constructing five ensemble models, which are then included in the base model pool. Finally, a promising surrogate model is adaptively selected from the base model pool based on its minimum RMSE. While ASMEA only adaptively selects the models, it fixes the pre-screening as the infill criterion. Moreover, its computational efficiency is low since it needs to conduct cross-validation for each base model. ESA [25] first constructs a pool of four different infill criteria, including DE evolutionary screening, surrogate-assisted local search, full-crossover strategy, and surrogate-assisted trust-region local search. Among them, the DE evolutionary screening strategy prefers exploration, the surrogate-assisted local search and trust-region local search strategies use different local search methods to favor exploitation, and the full-crossover strategy integrates good genes from historical solutions. Moreover, \\( Q \\) -learning is used to adjust the selection probability of each infill criterion through feedback information received during the optimization process. However, ESA fixes the RBF as the surrogate model.\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 2, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: 1116 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->\n\nAdditionally, some adaptive-model SAEAs have been proposed to deal with expensive multiobjective optimization problems. For instance, KTA2 [23] divides the optimization process into three states (i.e., convergence-demand, diversity-demand, and uncertainty-demand states) and uses the distances from solutions to the estimated ideal point and the pure diversity indicator to estimate the state. Moreover, the infill criterion is adaptively chosen based on the estimated optimization state, which guides the solution selection for FEs by taking into account the requirements on convergence, diversity, and model uncertainty separately. RVMM [48] uses the GP as the surrogate model and develops an adaptive model management strategy to adaptively choose the convergence-related criterion and diversity-related criterion. The adaptive model management strategy is assisted by two sets of reference vectors. One set of adaptive reference vectors focuses on convergence, while the other set of fixed reference vectors concentrates on diversity. The GP and RBF models are the most popular surrogate models due to their high fidelity and simplicity. Generally, the RBF model is computationally more efficient than the GP model. However, the GP model can provide uncertain information about its predictions. Therefore, to take advantage of the GP and RBF models in dealing with different problems, IBEA-MS [24] designs an acceptable reliability tolerance criterion to adaptively determine whether to use the GP model or the RBF model in environmental selection. Specifically, when the uncertainty of the GP model exceeds the acceptable error, the RBF model is used. Otherwise, the GP models are used.\n\n## III. BACKGROUND\n\n## A. Surrogate Models\n\n1) GP Model: Given a training data set \\( {\\left\\{  {\\mathbf{x}}_{i},f\\left( {\\mathbf{x}}_{i}\\right) \\right\\}  }_{i = 1}^{N} \\) ,GP predicts the output \\( {f}_{\\mathrm{{GP}}}\\left( \\mathbf{x}\\right) \\) of a solution \\( \\mathbf{x} \\) in the way of: \\( {f}_{\\mathrm{{GP}}}\\left( \\mathbf{x}\\right)  = \\mu \\left( \\mathbf{x}\\right)  + \\epsilon \\left( \\mathbf{x}\\right) \\) ,where \\( \\mu \\left( \\mathbf{x}\\right) \\) represents a global trend of the training data,and \\( \\epsilon \\left( \\mathbf{x}\\right)  \\sim  \\mathcal{N}\\left( {0,{\\sigma }^{2}\\left( \\mathbf{x}\\right) }\\right) \\) is a normal distribution. \\( \\mu \\left( \\mathbf{x}\\right) \\) and \\( {\\sigma }^{2}\\left( \\mathbf{x}\\right) \\) are estimated as follows [40]:\n\n\\[\\mu \\left( \\mathbf{x}\\right)  = \\mathbf{k}{\\left( \\mathbf{x}\\right) }^{\\top }{\\mathbf{K}}^{-1}\\mathbf{F} \\tag{2}\\]\n\n\\[{\\sigma }^{2}\\left( \\mathbf{x}\\right)  = \\kappa \\left( \\mathbf{x}\\right)  - \\mathbf{k}{\\left( \\mathbf{x}\\right) }^{\\top }{\\mathbf{K}}^{-1}\\mathbf{k}\\left( \\mathbf{x}\\right)  \\tag{3}\\]\n\nwhere \\( \\mathbf{k}\\left( \\mathbf{x}\\right)  = {\\left\\lbrack  C\\left( \\mathbf{x},{\\mathbf{x}}_{1}\\right) ,\\ldots ,C\\left( \\mathbf{x},{\\mathbf{x}}_{N}\\right) \\right\\rbrack  }^{\\top };\\mathbf{K} \\) is an \\( N \\times  N \\) matrix and \\( {K}_{i,j} = C\\left( {{\\mathbf{x}}_{i},{\\mathbf{x}}_{j}}\\right) ;\\kappa \\left( \\mathbf{x}\\right)  = C\\left( {\\mathbf{x},\\mathbf{x}}\\right) \\) ,and \\( \\mathbf{F} = \\) \\( {\\left\\lbrack  f\\left( {\\mathbf{x}}_{1}\\right) ,\\ldots ,f\\left( {\\mathbf{x}}_{N}\\right) \\right\\rbrack  }^{\\top } \\) . The covariance function \\( C\\left( {\\cdot , \\cdot  }\\right) \\) can be commonly calculated as follows:\n\n\\[C\\left( {{\\mathbf{x}}_{i},{\\mathbf{x}}_{j}}\\right)  = \\exp \\left( {-\\mathop{\\sum }\\limits_{{d = 1}}^{D}{\\theta }_{d}{\\left| {x}_{i,d} - {x}_{j,d}\\right| }^{2}}\\right)  \\tag{4}\\]\n\nwhere the hyper-parameters \\( \\theta \\) can be obtained by maximizing the likelihood function [33].\n\nFinally,for an unknown solution \\( \\mathbf{x} \\) ,its predicted fitness value \\( {\\widehat{f}}_{\\mathrm{{GP}}}\\left( \\mathbf{x}\\right) \\) and uncertainty \\( \\widehat{s}\\left( \\mathbf{x}\\right) \\) are given in the following:\n\n\\[{\\widehat{f}}_{\\mathrm{{GP}}}\\left( \\mathbf{x}\\right)  = \\mu \\left( \\mathbf{x}\\right)  + \\mathbf{k}{\\left( \\mathbf{x}\\right) }^{\\top }{\\mathbf{K}}^{-1}\\left( {\\mathbf{F} - \\mathbf{I}\\mu \\left( \\mathbf{x}\\right) }\\right)  \\tag{5}\\]\n\n\\[{\\widehat{s}}^{2}\\left( \\mathbf{x}\\right)  = {\\sigma }^{2}\\left( \\mathbf{x}\\right) \\left\\lbrack  {1 - \\mathbf{k}{\\left( \\mathbf{x}\\right) }^{\\top }{\\mathbf{K}}^{-1}\\mathbf{k}\\left( \\mathbf{x}\\right)  + \\frac{{\\left( 1 - {\\mathbf{I}}^{\\top }{\\mathbf{K}}^{-1}\\mathbf{k}\\left( \\mathbf{x}\\right) \\right) }^{2}}{{\\mathbf{I}}^{\\top }{\\mathbf{K}}^{-1}\\mathbf{I}}}\\right\\rbrack  \\]\n\n(6)\n\nwhere \\( \\mathbf{I} \\) is an \\( N \\times  1 \\) unit vector,and \\( \\widehat{s}\\left( \\mathbf{x}\\right)  = \\sqrt{{\\widehat{s}}^{2}\\left( \\mathbf{x}\\right) } \\) is the RMSE.\n\n2) RBF Model: The RBF model uses a linear combination of basis functions to approximate the fitness landscape. For the given training data set \\( {\\left\\{  {\\mathbf{x}}_{i},f\\left( {\\mathbf{x}}_{i}\\right) \\right\\}  }_{i = 1}^{N} \\) ,the form of the RBF model can be formulated as follows [49]:\n\n\\[{\\widehat{f}}_{\\mathrm{{RBF}}}\\left( \\mathbf{x}\\right)  = \\mathop{\\sum }\\limits_{{i = 1}}^{N}{w}_{i}\\varphi \\left( \\begin{Vmatrix}{\\mathbf{x} - {\\mathbf{x}}_{i}}\\end{Vmatrix}\\right)  + {b}_{0} + \\mathop{\\sum }\\limits_{{j = 1}}^{D}{b}_{j}{x}_{j} \\tag{7}\\]\n\nwhere \\( \\mathbf{w} = {\\left( {w}_{1},{w}_{2},\\ldots ,{w}_{N}\\right) }^{\\top } \\) is the weight vector of the basis function, and this article adopts the cubic function \\( \\varphi \\left( \\begin{Vmatrix}{\\mathbf{x} - {\\mathbf{x}}_{i}}\\end{Vmatrix}\\right)  = {\\left( \\begin{Vmatrix}\\mathbf{x} - {\\mathbf{x}}_{i}\\end{Vmatrix}\\right) }^{3} \\) ,where \\( \\parallel  \\cdot  \\parallel \\) denotes the Euclidean distance. \\( \\mathbf{b} = {\\left( {b}_{0},{b}_{1},\\ldots ,{b}_{D}\\right) }^{\\top } \\) is the coefficient of the first-order polynomial.\n\nThe parameters \\( \\mathbf{w} \\) and \\( \\mathbf{b} \\) in (7) can be obtained as follows:\n\n\\[\\left\\lbrack  \\begin{array}{l} \\mathbf{w} \\\\  \\mathbf{b} \\end{array}\\right\\rbrack   = {\\left\\lbrack  \\begin{matrix} \\mathbf{\\Phi } & \\mathbf{P} \\\\  {\\mathbf{P}}^{\\top } & {\\mathbf{0}}_{\\left( {D + 1}\\right)  \\times  \\left( {D + 1}\\right) } \\end{matrix}\\right\\rbrack  }^{ \\dagger  }\\left\\lbrack  \\begin{matrix} \\mathbf{F} \\\\  {\\mathbf{0}}_{\\left( {D + 1}\\right)  \\times  1} \\end{matrix}\\right\\rbrack   \\tag{8}\\]\n\nwhere \\( {\\Phi }_{i,j} = \\varphi \\left( {\\begin{Vmatrix}{\\mathbf{x}}_{i} - {\\mathbf{x}}_{j}\\end{Vmatrix}}_{2}\\right) ,i,j = 1,2,\\ldots ,N;\\mathbf{P} = \\) \\( {\\left\\lbrack  {\\mathbf{P}}_{1},\\ldots ,{\\mathbf{P}}_{N}\\right\\rbrack  }^{\\top } \\) and \\( {\\mathbf{P}}_{i} = {\\left\\lbrack  1,{x}_{i,1},\\ldots ,{x}_{i,D}\\right\\rbrack  }^{\\top };{\\mathbf{0}}_{\\left( {D + 1}\\right)  \\times  \\left( {D + 1}\\right) } \\) is a zero matrix of \\( \\left( {D + 1}\\right)  \\times  \\left( {D + 1}\\right) \\) ,and \\( \\dagger \\) is the generalized inverse,and \\( \\mathbf{F} = {\\left\\lbrack  f\\left( {\\mathbf{x}}_{1}\\right) ,\\ldots ,f\\left( {\\mathbf{x}}_{N}\\right) \\right\\rbrack  }^{\\top } \\) .\n\n3) PRS Model: The commonly used second-order polynomial is defined as follows [9]:\n\n\\[{\\widehat{f}}_{\\mathrm{{PRS}}}\\left( \\mathbf{x}\\right)  = {\\beta }_{0} + \\mathop{\\sum }\\limits_{{i = 1}}^{D}{\\beta }_{i}{x}_{i} + \\mathop{\\sum }\\limits_{{i = 1}}^{D}\\mathop{\\sum }\\limits_{{j \\geq  i}}^{D}{\\beta }_{ij}{x}_{i}{x}_{j} \\tag{9}\\]\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 3, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: XIE et al.: SAEA 1117-->\n\nwhere coefficients \\( {\\beta }_{0},{\\beta }_{i} \\) ,and \\( {\\beta }_{ij} \\) are regression parameters for the intercept, the linear term, and the quadratic term, respectively.\n\nFor the given training data set \\( {\\left\\{  {\\mathbf{x}}_{i},f\\left( {\\mathbf{x}}_{i}\\right) \\right\\}  }_{i = 1}^{N} \\) ,the unknown coefficients of the above polynomial model \\( \\mathbf{\\beta } = \\left( {{\\beta }_{0},\\ldots }\\right. \\) , \\( {\\beta }_{D},{\\beta }_{1,1},\\ldots ,{\\beta }_{1,D},{\\beta }_{2,3},\\ldots ,{\\beta }_{2,D},\\ldots ,{\\beta }_{D - 1,D},{\\beta }_{D,D}{)}^{\\top } \\) can be obtained by the least square method as follows [50]:\n\n\\[\\mathbf{\\beta } = {\\left( {\\mathbf{P}}^{\\top }\\mathbf{P}\\right) }^{-1}{\\mathbf{P}}^{\\top }\\mathbf{F} \\tag{10}\\]\n\nwhere \\( \\mathbf{P} = {\\left\\lbrack  {\\mathbf{P}}_{1},\\ldots ,{\\mathbf{P}}_{N}\\right\\rbrack  }^{\\top },{\\mathbf{P}}_{i} = {\\left\\lbrack  1,{x}_{i,1},\\ldots ,{x}_{i,D}\\right\\rbrack  }^{\\top } \\) ,and \\( \\mathbf{F} = \\) \\( {\\left\\lbrack  f\\left( {\\mathbf{x}}_{1}\\right) ,\\ldots ,f\\left( {\\mathbf{x}}_{N}\\right) \\right\\rbrack  }^{\\top } \\) .\n\n4) KNN Model: KNN is a popular classifier [29], [44]. Based on a training data set \\( {\\left\\{  {\\mathbf{x}}_{i},f\\left( {\\mathbf{x}}_{i}\\right) ,y\\left( {\\mathbf{x}}_{i}\\right) \\right\\}  }_{i = 1}^{N} \\) ,where \\( f\\left( {\\mathbf{x}}_{i}\\right) \\) and \\( y\\left( {\\mathbf{x}}_{i}\\right) \\) denote the objective function value and class/level of the solution \\( {\\mathbf{x}}_{i} \\) ,respectively. For a new solution \\( \\mathbf{x} \\) ,its class/level is predicted as follows:\n\n\\[{y}_{\\mathrm{{KNN}}}\\left( \\mathbf{x}\\right)  = \\operatorname{mode}\\left( \\left\\{  {y\\left( {\\mathbf{x}}_{1}\\right) ,\\ldots ,y\\left( {\\mathbf{x}}_{K}\\right) }\\right\\}  \\right)  \\tag{11}\\]\n\nwhere \\( {\\mathbf{x}}_{1},\\ldots ,{\\mathbf{x}}_{K} \\) are \\( K \\) nearest neighbors of \\( \\mathbf{x} \\) ,and mode(   ) denotes the mode of a set. In this article, \\( K \\) is set to 1 [29],[44],which means that the new solution \\( \\mathbf{x} \\) is assigned the same class as its nearest neighbor.\n\nIn this article, all surrogate models are trained based on the current population \\( \\mathcal{P} \\) in each iteration.\n\n## B. Infill Criteria\n\nSince the surrogate models are tightly coupled with the infill criteria in SAEAs, the corresponding infill criteria for the surrogate models mentioned in the previous section are introduced as follows.\n\n1) Infill Criteria for the GP Model: The LCB and EI criteria are commonly combined with GP for determining new solutions for FEs, which are expressed sequentially as follows [13], [33]:\n\n\\[{\\mathbf{x}}^{ * } = \\underset{\\mathbf{x} \\in  \\mathcal{X}}{\\arg \\min }\\left( {{\\widehat{f}}_{\\mathrm{{GP}}}\\left( \\mathbf{x}\\right)  - w\\widehat{s}\\left( \\mathbf{x}\\right) }\\right)  \\tag{12}\\]\n\nwhere \\( w \\) is set to 2 in this article [13],[27]. \\( \\mathcal{X} \\) denotes a set of unknown solutions.\n\n\\[{\\mathbf{x}}^{ * } = \\underset{\\mathbf{x} \\in  \\mathcal{X}}{\\arg \\max }\\left( {\\left( {{f}_{\\min } - {\\widehat{f}}_{\\mathrm{{GP}}}\\left( \\mathbf{x}\\right) }\\right) \\Phi \\left( Z\\right)  + \\widehat{s}\\left( \\mathbf{x}\\right) \\varphi \\left( Z\\right) }\\right)  \\tag{13}\\]\n\nwhere \\( Z = \\left( {\\left\\lbrack  {{f}_{\\min } - {\\widehat{f}}_{\\mathrm{{GP}}}\\left( \\mathbf{x}\\right) }\\right\\rbrack  /\\left\\lbrack  {\\widehat{s}\\left( \\mathbf{x}\\right) }\\right\\rbrack  }\\right) ,{f}_{\\min } \\) is the current minimum objective function value,and \\( \\Phi \\left( \\cdot \\right) \\) and \\( \\varphi \\left( \\cdot \\right) \\) are the density and cumulative distribution functions of standard normal distribution, respectively.\n\nThis article adopts the DE mutation and crossover operators to generate the new solution set \\( \\mathcal{X} = \\left\\{  {{\\mathbf{o}}_{1},\\ldots ,{\\mathbf{o}}_{N}}\\right\\} \\) ,where \\( N \\) is the population size. To be specific, assume that the current population is \\( \\mathcal{P},{\\mathbf{o}}_{i} = \\left( {{o}_{i,1},\\ldots ,{o}_{i,D}}\\right) \\) is generated as follows:\n\n\\[{\\mathbf{v}}_{i} = {\\mathbf{x}}_{i} + F \\times  \\left( {{\\mathbf{x}}_{b} - {\\mathbf{x}}_{i}}\\right)  + F \\times  \\left( {{\\mathbf{x}}_{r1} - {\\mathbf{x}}_{r2}}\\right)  \\tag{14}\\]\n\n\\[{o}_{i,j} = \\left\\{  \\begin{array}{l} {v}_{i,j},\\text{ if }{\\operatorname{rand}}_{i,j} \\leq  {CR}\\text{ or }j = {j}_{\\text{rand }} \\\\  {x}_{i,j},\\text{ otherwise } \\end{array}\\right.  \\tag{15}\\]\n\nwhere \\( {\\mathbf{x}}_{i} \\) is the \\( i \\) th solution in \\( \\mathcal{P},{\\mathbf{x}}_{r1} \\) and \\( {\\mathbf{x}}_{r2} \\) are randomly selected from the current population \\( \\mathcal{P},{\\mathbf{x}}_{b} \\) is the best solution of \\( \\mathcal{P};F \\) is the scale factor,and it is set to 0.5 in this article. rand \\( {}_{i,j} \\) and \\( {j}_{\\text{rand }} \\) are randomly selected from \\( \\left\\lbrack  {0,1}\\right\\rbrack \\) and \\( \\{ 1,\\ldots ,D\\} \\) ,respectively,and \\( {CR} \\in  \\left\\lbrack  {0,1}\\right\\rbrack \\) denotes the crossover rate, which is set to 0.9 in this article.\n\nNote that if \\( {o}_{i,j} \\) violates the boundary constraints,it will be repaired as follows:\n\n\\[{o}_{i,j} = {x}_{l,j} + \\operatorname{rand} \\times  \\left( {{x}_{u,j} - {x}_{l,j}}\\right) . \\tag{16}\\]\n\n2) Infill Criteria for the RBF and PRS Models: For all regression models (e.g., RBF and PRS), prescreening and local search are widely used in SAEAs, which are formulated as follows [28], [51]:\n\n\\[{\\mathbf{x}}^{ * } = \\underset{\\mathbf{x} \\in  \\mathcal{X}}{\\arg \\min }{\\widehat{f}}_{\\mathrm{{RBF}}/\\mathrm{{PRS}}}\\left( \\mathbf{x}\\right)  \\tag{17}\\]\n\nwhere \\( \\mathbf{x} \\) and \\( {\\widehat{f}}_{\\mathrm{{RBF}}/\\mathrm{{PRS}}}\\left( \\mathbf{x}\\right) \\) denote the unknown solution and its predicted fitness value by RBF/PRS, respectively. It should be noted that if \\( \\mathcal{X} \\) is a finite set of solutions,(17) belongs to prescreening,and it denotes a local search if \\( \\mathcal{X} \\) is a subspace of the search space.\n\nIn this article, the above DE operator is used to generate \\( \\mathcal{X} \\) for the prescreening. For the local search, \\( \\mathcal{X} \\) is defined as follows:\n\n\\[\\mathcal{X} = \\left\\lbrack  {\\mathbf{{lb}},\\mathbf{{ub}}}\\right\\rbrack   \\tag{18}\\]\n\nwhere \\( \\mathbf{{lb}} = {\\left( {l}_{1},\\ldots ,{l}_{D}\\right) }^{\\top } \\) and \\( \\mathbf{{ub}} = {\\left( {u}_{1},\\ldots ,{u}_{D}\\right) }^{\\top },{l}_{j} = \\) \\( \\min \\left\\{  {{x}_{i,j},{\\mathbf{x}}_{i} \\in  \\mathcal{P}}\\right\\} \\) ,and \\( {u}_{j} = \\max \\left\\{  {{x}_{i,j},{\\mathbf{x}}_{i} \\in  \\mathcal{P}}\\right\\}  ,j = 1,\\ldots ,D \\) . Moreover, DE is used to solve (17) in the local search in this article.\n\n3) Infill Criteria for the KNN Model: Classification models are often used to predict the level of the unknown solutions [52]. To this end, the current population is evenly divided into \\( L \\) levels \\( {\\mathcal{P}}^{1},\\ldots ,{\\mathcal{P}}^{L} \\) based on the fitness,and the best \\( \\left( {N/L}\\right) \\) solutions belong to the first level \\( {\\mathcal{P}}^{1} \\) . Two infill criteria (e.g., L1-exploitation and L1-exploration) have been designed for the classifier model. They are defined as follows [47].\n\n1) L1-Exploitation: The L1-exploitation criterion is defined as\n\n\\[{\\mathbf{x}}^{ * } = \\arg \\mathop{\\min }\\limits_{{\\mathbf{x} \\in  {\\mathcal{X}}^{1}}}\\max \\left\\{  {\\parallel \\mathbf{x} - \\mathbf{p}\\parallel ,\\mathbf{p} \\in  {\\mathcal{P}}^{1}}\\right\\}  . \\tag{19}\\]\n\n2) L1-Exploration: The L1-exploration is defined as\n\n\\[{\\mathbf{x}}^{ * } = \\arg \\mathop{\\max }\\limits_{{\\mathbf{x} \\in  {\\mathcal{X}}^{1}}}\\min \\left\\{  {\\parallel \\mathbf{x} - \\mathbf{p}\\parallel ,\\mathbf{p} \\in  {\\mathcal{P}}^{1}}\\right\\}   \\tag{20}\\]\n\nwhere \\( {\\mathcal{X}}^{1} \\) includes the solution in new generated solution set \\( \\mathcal{X} \\) whose predicted level by the KNN model is L1. In this article, \\( \\mathcal{X} \\) is also generated by the above-described DE operators, and the number of levels is set to 5 .\n\nFor the models and infill criteria discussed above, we have the following remarks. Over the past two decades, a variety of regression and classification models have been employed to aid evolutionary algorithms in solving complex optimization problems. As far as our knowledge goes, GP, RBF, PRS, and KNN are the most effective and commonly used models in state-of-the-art SAEAs [9], [19], [44], each with unique advantages in handling diverse problems. In this article, our goal is to develop an SAEA with auto-configuration of models and infill criteria. Therefore, we have chosen these well-known models for their established strengths. However, it should be noted that other models and infill criteria can also be easily incorporated into our proposed algorithm. Moreover, regarding the parameters involved in the infill criteria, we have tested several representative values for each parameter in our preliminary experiments. The experimental results show that they do not have a significant influence. Therefore, we use commonly accepted settings for these parameters in this article.\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 4, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: 1118 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->\n\n<!-- Media -->\n\n<!-- figureText: Arm set Arm Arm set Arm High-level Low-level (b) Cluster (a) -->\n\n<img src=\"./images/image_0_0c0d838e8bc635b81749a61a58c058e1.jpg\"/>\n\nFig. 1. Illustrative examples of HMAB clustering structures. (a) Disjoint clustering structure. (b) Hierarchical clustering structure.\n\n<!-- Media -->\n\n## C. Hierarchical Multiarmed Bandit\n\nMAB is a powerful tool for online learning [37]. In the standard MAB problem,the player must pick an arm \\( {a}_{t} \\) from a set of arms \\( \\mathcal{A} \\) at each time slot \\( t \\) and then obtain a reward \\( {r}_{{a}_{t}} \\) ,which is produced from a distribution that is unknown to the player. The performance of an algorithm in MAB is typically measured by the regret, and the player's goal is to minimize the expected cumulative regret over a sequence of \\( T \\) time slots. The expected cumulative regret can be expressed as follows [37]:\n\n\\[\\mathbb{E}\\left\\lbrack  {R}_{T}\\right\\rbrack   = \\mathop{\\sum }\\limits_{{t = 1}}^{T}{r}_{{a}_{t}^{ * }} - {r}_{{a}_{t}} \\tag{21}\\]\n\nwhere \\( {a}_{t}^{ * } \\) denotes the unknown best arm at the \\( t \\) -th time slot.\n\nHierarchical MAB (HMAB) is an extension of the standard MAB. Two commonly used hierarchical structures have been developed [39].\n\n1) Disjoint Clustering Structure: The \\( K \\) arms are classified into a set of clusters,and each arm \\( a \\in  \\mathcal{A} \\) belongs to only one cluster. The player first picks a cluster and then chooses an arm in the selected cluster. Finally, a reward is produced by the chosen arm. An illustrative example of disjoint clustering is shown in Fig. 1(a).\n\n2) Hierarchical Clustering Structure: The arms can be divided into multiple levels. The arms on the same level are different from each other, and the different high-level arms can be associated with the same low-level arms. The player first selects an arm from the high-level and then chooses an arm from its associated low-level. Finally, a reward is returned by all chosen arms cooperatively. An illustrative example of hierarchical clustering is shown in Fig. 1(b).\n\nMany theories and experiments have demonstrated that HMAB has significant advantages over standard MAB [37], [38], [39], [53], [54], [55]. In general, its benefits can be summarized into three aspects: 1) the regret bound of HMAB is lower than that of standard MAB; 2) HMAB can reduce the size of the arm space, especially when the arm space is large; and 3) HMAB can greatly mitigate the chances of selecting suboptimal arms, leading to improved performance in solving EOPs. Therefore, we propose integrating HMAB into SAEAs to enable the adaptive and cooperative selection of surrogate models and infill criteria. In this article, we consider the hierarchical clustering structure of HMAB, and its details will be explained in the following section.\n\n<!-- Media -->\n\nAlgorithm 1: AutoSAEA \\( \\left( {N,\\text{ MaxFEs },{\\mathcal{A}}^{H},{\\mathcal{A}}^{L},\\alpha }\\right) \\) .\n\n---\n\nInput:\n\tPopulation size: \\( N \\)\n\tMaximum number of FEs: MaxFEs\n\tHigh-level arm set: \\( {\\mathcal{A}}^{H} = \\left\\{  {{a}_{1}^{H},\\ldots ,{a}_{4}^{H}}\\right\\} \\)\n\tLow-level arm set: \\( {\\mathcal{A}}^{L} = \\left\\{  {{a}_{1}^{L},\\ldots ,{a}_{8}^{L}}\\right\\} \\)\n\tParameters: \\( \\alpha \\)\nInitialization:\n\tUse LHS to sample \\( N \\) solutions in the search space\n\tEvaluate and save them into the database \\( \\mathcal{D} \\)\n\tSet \\( {Q}_{a}^{H}\\left( 1\\right) ,a \\in  {\\mathcal{A}}^{H} \\) and \\( {Q}_{a}^{L},a \\in  {\\mathcal{A}}^{L} \\) to 0\n\tSet \\( {T}_{a}^{H}\\left( 1\\right) ,a \\in  {\\mathcal{A}}^{H} \\) and \\( {T}_{a}^{L},a \\in  {\\mathcal{A}}^{L} \\) to 0\n\tSet \\( \\mathcal{C}\\mathcal{A} \\) based on \\( {\\mathcal{A}}^{H} \\) and \\( {\\mathcal{A}}^{L} \\)\n\tSet \\( {FEs} = N \\) and \\( t = 1 \\)\nwhile \\( {FEs} < {MaxFEs} \\) do\n\tSelect \\( N \\) best solutions from \\( \\mathcal{D} \\) as the population \\( \\mathcal{P} \\)\n\tif \\( t \\leq  \\left| {\\mathcal{C}\\mathcal{A}}\\right| \\) then\n\t\tSelect the \\( t \\) -th combinatorial arm in \\( \\mathcal{{CA}} \\)\n\t\tSet \\( {a}_{t}^{H} \\) and \\( {a}_{t}^{L} \\) as the first and second arm in the\n\t\tselected combinatorial arm, respectively\n\telse\n\t\tChoose \\( {a}_{t}^{H} \\) and \\( {a}_{t}^{L} \\) by TL-UCB\n\tend if\n\tObtain \\( {\\mathbf{x}}_{t} \\) by \\( {a}_{t}^{H} \\) and \\( {a}_{t}^{L} \\) cooperatively\n\tEvaluate \\( {\\mathbf{x}}_{t} \\)\n\t\\( \\mathcal{D} = \\mathcal{D} \\cup  {\\mathbf{x}}_{t} \\)\n\t\\( {FEs} = {FEs} + 1 \\)\n\t\\( {T}_{{a}_{t}^{H}}^{H}\\left( {t + 1}\\right)  = {T}_{{a}_{t}^{H}}^{H}\\left( t\\right)  + 1,{T}_{{a}_{t}^{L}}^{L}\\left( {t + 1}\\right)  = {T}_{{a}_{t}^{L}}^{L}\\left( t\\right)  + 1 \\)\n\tUpdate the value of \\( {a}_{t}^{H} \\) and \\( {a}_{t}^{L} \\) by TL-R\n\t\\( t = t + 1 \\)\nend while\nOutput: The best solution in \\( \\mathcal{D} \\)\n\n---\n\n<!-- Media -->\n\n## IV. AUTOSAEA\n\n## A. Algorithm Structure\n\nThe framework of the proposed AutoSAEA is shown in Algorithm 1. Its input includes the population size \\( N \\) (line 2), the maximum number of FEs MaxFEs (line 3), a set of high-level arms \\( {\\mathcal{A}}^{H} \\) (line 4),a set of low-level arms \\( {\\mathcal{A}}^{L} \\) (line 5),and the parameter \\( \\alpha \\) (line 6). The high-level and low-level arms denote the surrogate model and the infill criterion, respectively. In this article,we set \\( {\\mathcal{A}}^{H} = \\left\\{  {{a}_{1}^{H} = \\mathrm{{GP}},{a}_{2}^{H} = \\mathrm{{RBF}},{a}_{3}^{H} = }\\right. \\) PRS, \\( \\left. {{a}_{4}^{H} = \\mathrm{{KNN}}}\\right\\} \\) and \\( {\\mathcal{A}}^{L} = \\left\\{  {{a}_{1}^{L} = \\mathrm{{LCB}},{a}_{2}^{L} = \\mathrm{{EI}},{a}_{3}^{L} = }\\right. \\) prescreening, \\( {a}_{4}^{L} = \\) local search, \\( {a}_{5}^{L} = \\) prescreening, \\( {a}_{6}^{L} = \\) local search, \\( {a}_{7}^{L} = \\mathrm{L}1 \\) -exploitation, \\( {a}_{8}^{L} = \\mathrm{L}1 \\) -exploration \\( \\} \\) . \\( {}^{1} \\)\n\n---\n\n<!-- Footnote -->\n\n\\( {}^{1} \\) Note that \\( {a}_{3}^{L} \\) and \\( {a}_{4}^{L} \\) are associated with RBF,and \\( {a}_{5}^{L} \\) and \\( {a}_{6}^{L} \\) are associated with PRS.\n\n<!-- Footnote -->\n\n---\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 5, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: XIE et al.: SAEA 1119-->\n\nIn the initialization (lines 7-13), an initial population with \\( N \\) solutions is generated by the Latin hypercube sampling (LHS) method (line 8). These solutions are evaluated directly and saved into a database \\( \\mathcal{D} \\) (line 9). The value for each arm and the number of selections for each arm is set to 0 (lines 10 and 11). Moreover, to make each arm be selected at least once,a legal combinatorial arm set is set as \\( \\mathcal{C}\\mathcal{A} \\) \\( = \\{ \\left( {\\mathrm{{GP}},\\mathrm{{LCB}}}\\right) ,\\left( {\\mathrm{{GP}},\\mathrm{{EI}}}\\right) ,(\\mathrm{{RBF}}, \\) prescreening), \\( (\\mathrm{{RBF}}, \\) local search), (PRS, prescreening), (PRS, local search), (KNN, L1- exploitation), (KNN, L1-exploration)\\} (line 12). Finally, the current number of FEs and the iteration number are set to \\( N \\) and 1, respectively (line 13).\n\nIn the optimization process (lines 14-29), it first selects the \\( N \\) best solutions from \\( \\mathcal{D} \\) as the population \\( \\mathcal{P} \\) (line 15). If the number of iterations \\( t \\) is less than the cardinality of the combinatorial arm set \\( \\mathcal{{CA}} \\) (line 16),the \\( t \\) -th combinatorial arm in \\( \\mathcal{{CA}} \\) is chosen (line 17). Then,the high-level arm \\( {a}_{t}^{H} \\) and low-level arm \\( {a}_{t}^{L} \\) are set to the first and second arms in the selected combinatorial arm (line 18), respectively. Otherwise, they are determined by the TL-UCB method (line 20). When the high-level arm \\( {a}_{t}^{H} \\) and low-level arm \\( {a}_{t}^{L} \\) are selected,a new solution \\( {\\mathbf{x}}_{t} \\) is determined by them cooperatively (line 22). To be specific:\n\n1) when \\( {a}_{t}^{H} \\) represents the GP model,an offspring population with \\( N \\) solutions is first generated by DE operators. Then, the offspring solution with the best LCB value (if \\( {a}_{t}^{L} = \\mathrm{{LCB}} \\) ) or best EI value (if \\( {a}_{t}^{L} = \\mathrm{{EI}} \\) ) is chosen as \\( {\\mathbf{x}}_{t} \\) ;\n\n2) when \\( {a}_{t}^{H} \\) represents the RBF model or PRS model,if \\( {a}_{t}^{L} = \\) prescreening,an offspring population with \\( N \\) solutions is first generated by DE operators. Then, the offspring solution with the best \\( {\\widehat{f}}_{\\mathrm{{RBF}}}\\left( \\mathbf{x}\\right) \\) value (if \\( {a}_{t}^{H} = \\mathrm{{RBF}} \\) ) or \\( {\\widehat{f}}_{\\mathrm{{PRS}}}\\left( \\mathbf{x}\\right) \\) value (if \\( {a}_{t}^{H} = \\) PRS) is chosen as \\( {\\mathbf{x}}_{t} \\) . If \\( {a}_{t}^{L} = \\) local search,DE is used to optimize \\( {\\widehat{f}}_{\\mathrm{{RBF}}}\\left( \\mathbf{x}\\right) \\) (if \\( {a}_{t}^{H} = \\mathrm{{RBF}} \\) ) or \\( {\\widehat{f}}_{\\mathrm{{PRS}}}\\left( \\mathbf{x}\\right) \\) (if \\( {a}_{t}^{H} = \\) PRS) to get \\( {\\mathbf{x}}_{t} \\) . Note that in this article, the DE local search is conducted with a population size of \\( N \\) and a maximum number of \\( {100D} + {1000} \\) generations [25];\n\n3) when \\( {a}_{t}^{H} \\) represents the KNN model,an offspring population with \\( N \\) solutions is first generated using DE operators. Then, \\( {\\mathbf{x}}_{t} \\) is selected based on L1-exploitation (if \\( {a}_{t}^{L} = \\mathrm{L}1 \\) -exploitation) or \\( \\mathrm{L}1 \\) -exploration (if \\( {a}_{t}^{L} = \\) L1-exploration) from these offspring solutions.\n\nThen, \\( {\\mathbf{x}}_{t} \\) is evaluated and added into the database \\( \\mathcal{D} \\) (lines 23 and 24), and the FEs is increased by 1 (line 25). Finally, the number of selections for \\( {a}_{t}^{H} \\) and \\( {a}_{t}^{L} \\) is increased by 1 (line 26), and their values are updated by the TL-R method (line 27).\n\nWhen the maximum computational budget is consumed, the best solution in the database \\( \\mathcal{D} \\) is output as the final solution (line 30). In the following, we will give a detailed introduction to the two core components, TL-UCB and TL-R, sequentially.\n\n## B. TL-UCB\n\nTL-UCB is the policy to select the high-level arm (surrogate model) and low-level arm (infill criterion). To choose a suitable high-level arm, the high-level UCB is executed, which is formulated as follows:\n\n\\[{a}_{t}^{H} = \\underset{a \\in  {\\mathcal{A}}^{H}}{\\arg \\max }\\left\\lbrack  {{Q}_{a}^{H}\\left( t\\right)  + \\sqrt{\\frac{\\alpha \\ln \\left( t\\right) }{{T}_{a}^{H}\\left( t\\right) }}}\\right\\rbrack   \\tag{22}\\]\n\n<!-- Media -->\n\nAlgorithm 2: \\( \\left( {{a}_{t}^{H},{a}_{t}^{L}}\\right)  = \\) TL-UCB \\( \\left( {{\\mathcal{A}}^{H},{\\mathcal{A}}^{L},t,{T}^{H},{T}^{L},{Q}^{H}\\left( t\\right) }\\right. \\) , \\( {Q}^{L}\\left( t\\right) ,\\alpha ) \\) .\n\n---\n\nInput:\n\tHigh-level arms set: \\( {\\mathcal{A}}^{H} = \\left\\{  {{a}_{1}^{H},\\ldots ,{a}_{4}^{H}}\\right\\} \\)\n\tLow-level arm set: \\( {\\mathcal{A}}^{L} = \\left\\{  {{a}_{1}^{L},\\ldots ,{a}_{8}^{L}}\\right\\} \\)\n\tCurrent number of iterations: \\( t \\)\n\tNumber of selection for each arm: \\( {T}^{H}\\left( t\\right) \\) and \\( {T}^{L}\\left( t\\right) \\)\n\tThe value for each arm: \\( {Q}^{H}\\left( t\\right) \\) and \\( {Q}^{L}\\left( t\\right) \\)\n\tParameter: \\( \\alpha \\)\n\tSet \\( {A}_{{a}_{1}^{H}}^{L} = \\left\\{  {{a}_{1}^{L},{a}_{2}^{L}}\\right\\}  ,{A}_{{a}_{2}^{H}}^{L} = \\left\\{  {{a}_{3}^{L},{a}_{4}^{L}}\\right\\} \\) ,\n\t\t\t\\( {A}_{{a}_{3}^{H}}^{{L}^{1}} = \\left\\{  {{a}_{5}^{L},{a}_{6}^{L}}\\right\\}  ,{A}_{{a}_{4}^{H}}^{{L}^{2}} = \\left\\{  {{a}_{7}^{L},{a}_{8}^{L}}\\right\\} \\)\n\t\\( {a}_{t}^{H} = \\underset{a \\in  {\\mathcal{A}}^{H}}{\\arg \\max }\\left\\lbrack  {{Q}_{a}^{H}\\left( t\\right)  + \\sqrt{\\frac{\\alpha \\ln \\left( t\\right) }{{T}_{a}^{H}\\left( t\\right) }}}\\right\\rbrack \\)\n\t\\( {a}_{t}^{L} = \\underset{a \\in  {\\mathcal{A}}_{{a}_{t}^{H}}^{L}}{\\arg \\max }\\left\\lbrack  {{Q}_{a}^{L}\\left( t\\right)  + \\sqrt{\\frac{\\alpha \\ln \\left( t\\right) }{{T}_{a}^{L}\\left( t\\right) }}}\\right\\rbrack \\)\nOutput: High-level arm \\( {a}_{t}^{H} \\) and low-level arm \\( {a}_{t}^{L} \\)\n\n---\n\n<!-- figureText: High-level RBF Arm set PRS search Prescreening search xploitation Low-level LCB EI Prescreening -->\n\n<img src=\"./images/image_1_f83d2948a7d7bc40429fbc9934b88248.jpg\"/>\n\nFig. 2. Association relationship between the high-level arms and low-level ones.\n\n<!-- Media -->\n\nwhere \\( {Q}_{a}^{H}\\left( t\\right) \\) denotes the value of the high-level arm \\( a \\) at the \\( t \\) -th iteration and \\( {T}_{a}^{H}\\left( t\\right) \\) is the number of selections of the high-level arm \\( a \\) during the past \\( t - 1 \\) iterations, \\( t \\) is the current iteration number,and \\( \\alpha \\) is a control parameter used to balance the tradeoff between exploiting well-performing arms and exploring rarely selected arms.\n\nWhen the high-level arm is determined, the low-level arm is picked up by the low-level UCB, which is defined as follows:\n\n\\[{a}_{t}^{L} = \\underset{a \\in  {\\mathcal{A}}_{{a}_{t}^{H}}^{L}}{\\arg \\max }\\left\\lbrack  {{Q}_{a}^{L}\\left( t\\right)  + \\sqrt{\\frac{\\alpha \\ln \\left( t\\right) }{{T}_{a}^{L}\\left( t\\right) }}}\\right\\rbrack   \\tag{23}\\]\n\nwhere \\( {Q}_{a}^{L}\\left( t\\right) \\) denotes the value of the low-level arm \\( a \\) in the \\( t \\) -th iteration, \\( {T}_{a}^{L}\\left( t\\right) \\) is the number of selections of the low-level arm \\( a \\) during the past \\( t - 1 \\) iterations. Here, it should be noted that not each high-level arm can be associated with all low-level ones. Therefore, \\( {\\mathcal{A}}_{{a}_{r}^{H}}^{L} \\) denotes the low-level arms that can be associated with the high-level arm \\( {a}_{t}^{H} \\) . In this article,the association relationship between the high-level arms and low-level arms is shown in Fig. 2.\n\nMoreover, the pseudo-code of the TL-UCB is provided in Algorithm 2.\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 6, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: 1120 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->\n\n## C. TL-R\n\nAfter determining the high-level arm (surrogate model) and low-level arm (infill criterion), the corresponding surrogate model is trained based on the current population \\( \\mathcal{P} \\) . Then,a new solution \\( {\\mathbf{x}}_{t} \\) is obtained by the selected high-level and low-level arms cooperatively. To measure the optimization utility of the chosen \\( {a}_{t}^{L} \\) and \\( {a}_{t}^{H} \\) ,a TL-R is designed as follows.\n\n1) Low-Level Reward:\n\n\\[{r}_{{a}_{t}^{L}} =  - \\frac{1}{N}I\\left( {\\mathbf{x}}_{t}\\right)  + \\frac{N + 1}{N} \\tag{24}\\]\n\nwhere \\( N \\) is the population size and \\( I\\left( {\\mathbf{x}}_{t}\\right) \\) denotes the ranking of \\( {\\mathbf{x}}_{t} \\) in the current population \\( \\mathcal{P} \\) . Specifically,if \\( {\\mathbf{x}}_{t} \\) is the current best solution, \\( I\\left( {\\mathbf{x}}_{t}\\right)  = 1 \\) such that \\( {r}_{{a}_{t}^{L}} = 1 \\) . On the contrary, if \\( I\\left( {\\mathbf{x}}_{t}\\right)  = N + 1 \\) such that \\( {r}_{{a}_{t}^{L}} = 0 \\) . The low-level reward \\( {r}_{{a}_{t}^{L}} \\) has four characteristics: 1) \\( {r}_{{a}_{t}^{L}} \\) is bounded in \\( \\left\\lbrack  {0,1}\\right\\rbrack  ;2){r}_{{a}_{t}^{L}} \\) is linearly proportional to the ranking \\( I\\left( {\\mathbf{x}}_{t}\\right) \\) of the newly generated solution \\( {\\mathbf{x}}_{t} \\) ,the better the ranking,the larger the reward; 3) the low-level rewards are nonsparse; and 4) it remains stationary to some extent throughout the entire optimization process.\n\nBased on the low-level reward \\( {r}_{{a}_{t}^{L}} \\) ,the value of \\( {a}_{t}^{L} \\) is updated as follows:\n\n\\[{Q}_{{a}_{t}^{L}}^{L}\\left( {t + 1}\\right)  = \\frac{{T}_{{a}_{t}^{L}}^{L}\\left( t\\right) {Q}_{{a}_{t}^{L}}^{L}\\left( t\\right)  + {r}_{{a}_{t}^{L}}}{{T}_{{a}_{t}^{L}}^{L}\\left( t\\right)  + 1}. \\tag{25}\\]\n\nIt should be noted that the value of the high-level arm \\( {a}_{t}^{H} \\) is increased if and only if the low-level reward is larger than its current value [e.g., \\( {r}_{{a}_{t}^{L}} \\geq  {Q}_{{a}_{t}^{L}}^{L}\\left( t\\right) \\) ].\n\n2) High-Level Reward: After the value \\( {Q}_{{a}_{t}^{L}}^{L} \\) of the low-level arm \\( {a}_{t}^{L} \\) is updated,the reward for the high-level arm \\( {a}_{t}^{H} \\) is designed as follows:\n\n\\[{r}_{{a}_{t}^{H}} = \\frac{{Q}_{{a}_{t}^{L}}^{L}\\left( {t + 1}\\right)  - {Q}_{{a}_{t}^{L}}^{L}\\left( t\\right) }{\\left| {\\mathcal{A}}_{{a}_{t}^{H}}^{L}\\right| } \\tag{26}\\]\n\nwhere \\( {\\mathcal{A}}_{{a}^{H}}^{L} \\) denotes the low-level arm set that is associated with the high-level arm \\( {a}_{t}^{H} \\) . Clearly,the high-level reward \\( {r}_{{a}_{t}^{H}} \\) can be positive or negative. To be specific, if the value of the low-level arm \\( {a}_{t}^{L} \\) increases (e.g., \\( {Q}_{{a}_{t}^{L}}^{L}\\left( {t + 1}\\right)  - {Q}_{{a}_{t}^{L}}^{L}\\left( t\\right)  \\geq  0 \\) ), the reward of the high-level arm is positive. Otherwise, it is negative.\n\nBased on the high-level reward \\( {r}_{{a}_{t}^{H}} \\) ,the value of \\( {a}_{t}^{H} \\) is updated as follows:\n\n\\[{Q}_{{a}_{t}^{H}}^{H}\\left( {t + 1}\\right)  = {Q}_{{a}_{t}^{H}}^{H}\\left( t\\right)  + {r}_{{a}_{t}^{H}}. \\tag{27}\\]\n\nClearly,if the high-level reward of \\( {a}_{t}^{H} \\) is positive,its value will be increased. Otherwise, its value will be decreased. It should be pointed out that the value \\( {Q}_{{a}_{t}^{H}}^{H}\\left( {t + 1}\\right) \\) of \\( {a}_{t}^{H} \\) is the mean of the value of its associated low-level arms. To be specific, putting (26) into (27), we have\n\n\\[{Q}_{{a}_{t}^{H}}^{H}\\left( {t + 1}\\right)  = \\frac{\\left| {\\mathcal{A}}_{{a}_{t}^{H}}^{L}\\right|  \\cdot  {Q}_{{a}_{t}^{H}}^{H}\\left( t\\right)  + {Q}_{{a}_{t}^{L}}^{L}\\left( {t + 1}\\right)  - {Q}_{{a}_{t}^{L}}^{L}\\left( t\\right) }{\\left| {\\mathcal{A}}_{{a}_{t}^{H}}^{L}\\right| }. \\tag{28}\\]\n\n<!-- Media -->\n\n<!-- figureText: Arm set Arm High-level Low-level -->\n\n<img src=\"./images/image_2_d6897725e753e9b71a34fceb1b13a071.jpg\"/>\n\nFig. 3. Numerical example of reward propagation in TL-R.\n\nAlgorithm 3: \\( \\left( {{Q}_{{a}_{t}^{H}}^{H}\\left( {t + 1}\\right) ,{Q}_{{a}_{t}^{L}}^{L}\\left( {t + 1}\\right) }\\right)  = \\operatorname{TL-R}\\left( {N,\\mathcal{P},{\\mathbf{x}}_{t},{T}_{{a}_{t}^{L}}^{L}\\left( t\\right) }\\right. \\) , \\( \\left. {{Q}_{{a}_{t}^{L}}^{L}\\left( t\\right) ,{Q}_{{a}_{t}^{H}}^{H}\\left( t\\right) }\\right) \\) .\n\n---\n\nInput:\n\t\tPopulation size: \\( N \\)\n\t\tPopulation: \\( \\mathcal{P} \\)\n\t\tNew generated solution: \\( {\\mathbf{x}}_{t} \\)\n\t\tNumber of selections of \\( {a}_{t}^{L} : {T}_{{a}_{t}^{L}}^{L}\\left( t\\right) \\)\n\t\tThe value of \\( {a}_{t}^{L} \\) and \\( {a}_{t}^{H} : {Q}_{{a}_{t}^{L}}^{L}\\left( t\\right) \\) and \\( {Q}_{{a}_{t}^{H}}^{H}\\left( t\\right) \\)\n\t\tCalculate the low-level reward \\( {r}_{{a}_{t}^{L}} \\) of \\( {a}_{t}^{L} \\) as (24)\n\t\tCalculate \\( {Q}_{{a}_{t}^{L}}^{L}\\left( {t + 1}\\right) \\) of \\( {a}_{t}^{L} \\) as (25)\n\t\tCalculate the high-level reward \\( {r}_{{a}_{t}^{H}} \\) of \\( {a}_{t}^{H} \\) as (26)\n\tCalculate \\( {Q}_{{a}_{t}^{H}}^{H}\\left( {t + 1}\\right) \\) of \\( {a}_{t}^{H} \\) as (27)\n\tOutput: The updated values \\( {Q}_{{a}_{t}^{H}}^{H}\\left( {t + 1}\\right) \\) and \\( {Q}_{{a}_{t}^{L}}^{L}\\left( {t + 1}\\right) \\) of\n\\( {a}_{t}^{H} \\) and \\( {a}_{t}^{L} \\)\n\n---\n\n<!-- Media -->\n\nAssume that \\( \\left| {\\mathcal{A}}_{{a}_{t}^{H}}^{L}\\right|  \\cdot  {Q}_{{a}_{t}^{H}}^{H}\\left( t\\right)  = \\mathop{\\sum }\\limits_{{{a}^{L} \\in  {\\mathcal{A}}_{{a}_{t}^{H}}^{L}}}{Q}_{{a}^{L}}^{L}\\left( t\\right) \\) holds for the \\( t \\) -th generation. Therefore\n\n\\[{Q}_{{a}_{t}^{H}}^{H}\\left( {t + 1}\\right)  = \\frac{\\mathop{\\sum }\\limits_{{{a}^{L} \\in  {\\mathcal{A}}_{{a}_{t}^{H}}^{L}}}{Q}_{{a}^{L}}^{L}\\left( t\\right)  + {Q}_{{a}_{t}^{L}}^{L}\\left( {t + 1}\\right)  - {Q}_{{a}_{t}^{L}}^{L}\\left( t\\right) }{\\left| {\\mathcal{A}}_{{a}_{t}^{H}}^{L}\\right| }. \\tag{29}\\]\n\nSince the value of the un-selected low-level arm is not updated, \\( {Q}_{{a}^{L}}^{L}\\left( {t + 1}\\right)  = {Q}_{{a}^{L}}^{L}\\left( t\\right) ,\\left( {{a}^{L} \\neq  {a}_{t}^{L}}\\right) \\) . Finally,we have\n\n\\[{Q}_{{a}_{t}^{H}}^{H}\\left( {t + 1}\\right)  = \\frac{\\mathop{\\sum }\\limits_{{{a}^{L} \\in  {\\mathcal{A}}_{{a}_{t}^{H}}^{L}}}{Q}_{{a}^{L}}^{L}\\left( {t + 1}\\right) }{\\left| {\\mathcal{A}}_{{a}_{t}^{H}}^{L}\\right| }. \\tag{30}\\]\n\nSince the values of both the low-level arm and high-level arm are initialized to 0,the equation \\( \\left| {\\mathcal{A}}_{{a}_{t}^{H}}^{L}\\right|  \\cdot  {Q}_{{a}_{t}^{H}}^{H}\\left( t\\right)  = \\) \\( \\mathop{\\sum }\\limits_{{{a}^{L} \\in  {\\mathcal{A}}_{{a}^{H}}^{L}}}{Q}_{{a}^{L}}^{L}\\left( t\\right) \\) holds in the initialization (i.e., \\( t = 1 \\) ). Therefore,in each generation,the value of \\( {a}_{t + 1}^{H} \\) is exactly the mean of the values of its associated low-level arms.\n\nThe pseudocode of TL-R is provided in Algorithm 3. In addition, Fig. 3 illustrates a numerical example of reward propagation in TL-R. In this figure, we assume that at the \\( t \\) -th generation,TL-UCB selects the high-level arm \\( {a}_{t}^{H} \\) and low-level arm \\( {a}_{t}^{L} \\) ,and generates a solution \\( {\\mathbf{x}}_{t} \\) by them cooperatively. Suppose the low-level reward is \\( {r}_{{a}_{t}^{L}} = {0.8} \\) ,as computed using (24), and the value of the low-level arm is \\( {Q}_{{a}_{t}^{L}}^{L}\\left( {t + 1}\\right)  = {0.6} \\) ,as obtained from (25). Then,using (26),we can compute the reward of the high-level arm \\( {r}_{{a}_{t}^{H}} = {0.05} \\) ,and finally,the value of the high-level arm \\( {Q}_{{a}_{t}^{H}}^{H}\\left( {t + 1}\\right)  = {0.7} \\) is determined using (27). The path of the reward is indicated by solid red lines.\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 7, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: XIE et al.: SAEA 1121-->\n\nCompared with other existing adaptive-model SAEAs [22], [23], [24], [25], the proposed AutoSAEA has the following differences.\n\n1) Although the model and infill criterion configuration are common, they differ from those used in the existing adaptive-model SAEAs.\n\n2) AutoSAEA can adaptively choose the surrogate model and infill criterion in an online manner. However, existing adaptive-model SAEAs are either only for model selection or infill criterion selection but do not study adaptive selection for both cooperatively.\n\n3) AutoSAEA formulates the surrogate model and infill criterion selection as a TL-MAB. Moreover, a TL-R and a TL-UCB designed to effectively capture good coupling behavior between the surrogate model and infill criterion. The adopted online learning strategy of AutoSAEA is obviously different from those of the existing adaptive SAEAs.\n\n## V. Numerical Experiments\n\n## A. Benchmark Problems\n\nCommonly used benchmark problems to evaluate the performance of the SAEAs are the Ellipsoid function, Rosenbrock function, Ackley function, Griewank function, Ra<PERSON>rigin function, and a few problems from CEC2005 competition [14], [15], [16], [18], [19], [32], [47], [56]. However, most of these problems are simple and hardly represent the complex characteristics of the real-world EOPs. Therefore, we first adopt CEC2005 [56] and CEC2015 problems [57] with \\( D = {10} \\) and \\( D = {30} \\) to evaluate the performance of our proposed AutoSAEA. Moreover, performance is measured using the function error metric \\( f\\left( {\\mathbf{x}}_{ * }\\right)  - f\\left( {\\mathbf{x}}^{o}\\right) \\) ,where \\( {\\mathbf{x}}_{ * } \\) denotes the best solution found by the algorithm,and \\( {\\mathbf{x}}^{o} \\) is the real optimum. To analyze the significant difference between the results obtained by all test algorithms, the Wilcoxon rank-sum test and Friedman test with the Hommel post-hoc procedure are conducted at a significant level of 0.05 [58], [59]. The symbols \"+,\" \"=,\" and \"-\" denote that AutoSAEA is statistically better than, competitive with, and worse than the compared algorithm, respectively.\n\n## B. Comparison With Existing SAEAs\n\nIn this section, comparison experiments are conducted between AutoSAEA and two traditional Bayesian optimization methods (i.e., GP-LCB [43] and GP-EI [40]) and eight state-of-the-art SAEAs (i.e., IKAEA (2021) [42], ESAO (2019) [32], CA-LLSO (2020) [47], TS-DDEO (2021) [31], SA-MPSO (2021) [16], SAMFEO (2022) [58], GL-SADE (2022) [18], and ESA (2022) [25]). The parameters of all compared algorithms remain the same as in their original papers. For the proposed AutoSAEA,parameters \\( N \\) and \\( \\alpha \\) are set to 100 and 2.5, respectively. The statistical comparison results are obtained under 1000 FEs and 20 independent runs [15], [16], [18], [32], [47] on each problem. The mean and standard deviation of the obtained function error values for each algorithm on the CEC2005 10D problems, CEC2005 30D problems, CEC2015 10D problems, and CEC2015 30D problems are provided in Tables I-IV of the supplementary file, respectively, and the statistical results are shown in Table I.\n\nFor the CEC2005 10D problems, AutoSAEA exhibits significant advantages over all compared algorithms. Specifically, it outperforms GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 8, 9,12,13,10,14,11,11,14,and 9 out of 15 problems,respectively. Moreover, it is only worse than CA-LLSO on 1 out of 15 problems. Based on the Friedman test, AutoSAEA ranks first and significantly outperforms all compared algorithms except GP-LCB and ESA.\n\nFor the CEC2005 30D problems, AutoSAEA is significantly better than all compared algorithms in most cases. It outperforms GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 7, 8, 12,11,10,9,12,8,12,and 8 out of 15 problems,respectively. Moreover, it is only worse than GP-LCB, GP-EI, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on2,2,1,1,2,1,4,2,and 3 out of 15 problems. AutoSAEA takes first place and is significantly better than all compared algorithms except GP-LCB, GP-EI, SAMFEO, and ESA according to the Friedman test results.\n\nFor the CEC2015 10D problems, AutoSAEA achieves the best results in most cases. Specifically, it is significantly better than or at least as good as TS-DDEO and ESAO in all cases. Additionally, it is only worse than GP-LCB, GP-EI, IKAEA, CA-LLSO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 2, 2,1,1,1,2,2,and 2 out of 15 problems,respectively. Based on the statistical results of the Friedman test, AutoSAEA is the champion and significantly outperforms all compared SAEAs except GP-LCB, GP-EI, SAMFEO, and ESA.\n\nFor the CEC2015 30D problems, AutoSAEA is still the winner in the overall comparison of algorithms. It is significantly better than GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 5, 6,12,12,9,9,12,6,12,and 6 out of 15 cases,respectively. Moreover, it is only worse than GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 2, 3, 1, 0, 3, 2, 1, 1, 1, and 1 out of 15 problems, respectively. According to the statistical results of the Friedman test, AutoSAEA ranks first and is significantly better than IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, and GL-SADE.\n\nTo evaluate the performance of SAEAs, it is necessary to test them on various problems with different characteristics. It is well known that different models and infill criteria are suitable for solving different problems [23], [24], [25], [30]. The proposed AutoSAEA does not focus on developing new models or infill criteria but aims to leverage the existing well-established ones to improve the performance of SAEA. Therefore, although the surrogate model and infill criterion adopted in AutoSAEA are common configurations, the reason why it performs better than other state-of-the-art SAEAs is explained as follows.\n\n1) Compared With Single-Model SAEAs: Single-model SAEAs use a fixed surrogate model and infill criterion for all problems. While these algorithms can solve specific problems well, they often perform poorly on others. Therefore, when evaluating their performance on a sufficiently diverse set of problems, single-model SAEAs tend to perform worse than multiple-model SAEAs and adaptive-model SAEAs [18], [19], [35].\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 8, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: 1122 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->\n\n<!-- Media -->\n\nTABLE I\n\nCOMPARISON RESULTS BETWEEN AUTOSAEA AND ITS COMPETITORS AND THE RANKING OF EACH ALGORITHM ON CEC2005 PROBLEMS AND CEC2015 PROBLEMS WITH \\( D = {10} \\) AND \\( D = {30} \\)\n\n<table><tr><td colspan=\"12\">CEC2005 10D problems</td></tr><tr><td/><td>GP-LCB</td><td>GP-EI</td><td>IKAEA</td><td>ESAO</td><td>CA-LLSO</td><td>TS-DDEO</td><td>SA-MPSO</td><td>SAMFEO</td><td>GL-SADE</td><td>ESA</td><td>AutoSAEA</td></tr><tr><td>+/=/-</td><td>8/7/0</td><td>9/6/0</td><td>\\( {12}/3/0 \\)</td><td>13/2/0</td><td>10/4/1</td><td>14/1/0</td><td>11/4/0</td><td>11/4/0</td><td>14/1/0</td><td>9/6/0</td><td>NA</td></tr><tr><td>Ranking</td><td>4.2</td><td>5.0</td><td>7.3</td><td>8.7</td><td>6.3</td><td>7.2</td><td>6.7</td><td>5.9</td><td>7.8</td><td>4.1</td><td>1.9</td></tr><tr><td>\\( p \\) -value</td><td>0.12</td><td>0.03</td><td>0.0001</td><td>0</td><td>0.001</td><td>0.0001</td><td>0.0004</td><td>0.004</td><td>0</td><td>0.12</td><td>NA</td></tr><tr><td colspan=\"12\">CEC2005 30D problems</td></tr><tr><td/><td>GP-LCB</td><td>GP-EI</td><td>IKAEA</td><td>ESAO</td><td>CA-LLSO</td><td>TS-DDEO</td><td>SA-MPSO</td><td>SAMFEO</td><td>GL-SADE</td><td>ESA</td><td>AutoSAEA</td></tr><tr><td>+/=/-</td><td>7/6/2</td><td>8/5/2</td><td>12/3/0</td><td>11/3/1</td><td>10/4/1</td><td>9/4/2</td><td>12/2/1</td><td>8/3/4</td><td>12/1/2</td><td>8/4/3</td><td>NA</td></tr><tr><td>Ranking</td><td>4.7</td><td>4.6</td><td>8.8</td><td>8.0</td><td>6.7</td><td>6.1</td><td>7.5</td><td>4.1</td><td>7.8</td><td>5.0</td><td>2.7</td></tr><tr><td>\\( p \\) -value</td><td>0.30</td><td>0.30</td><td>0</td><td>0.0001</td><td>0.005</td><td>0.03</td><td>0.0004</td><td>0.30</td><td>0.0002</td><td>0.22</td><td>NA</td></tr><tr><td colspan=\"12\">CEC2015 10D problems</td></tr><tr><td/><td>GP-LCB</td><td>GP-EI</td><td>IKAEA</td><td>ESAO</td><td>CA-LLSO</td><td>TS-DDEO</td><td>SA-MPSO</td><td>SAMFEO</td><td>GL-SADE</td><td>ESA</td><td>AutoSAEA</td></tr><tr><td>+/=/-</td><td>7/6/2</td><td>7/6/2</td><td>11/3/1</td><td>14/1/0</td><td>12/2/1</td><td>11/4/0</td><td>12/2/1</td><td>11/2/2</td><td>12/1/2</td><td>10/3/2</td><td>NA</td></tr><tr><td>Ranking</td><td>4.5</td><td>4.1</td><td>6.3</td><td>9.1</td><td>7.7</td><td>7.7</td><td>5.9</td><td>5.0</td><td>7.7</td><td>5.1</td><td>2.6</td></tr><tr><td>\\( p \\) -value</td><td>0.23</td><td>0.23</td><td>0.01</td><td>0</td><td>0.0002</td><td>0.002</td><td>0.03</td><td>0.18</td><td>0.0002</td><td>0.18</td><td>NA</td></tr><tr><td colspan=\"12\">CEC2015 30D problems</td></tr><tr><td/><td>GP-LCB</td><td>GP-EI</td><td>IKAEA</td><td>ESAO</td><td>CA-LLSO</td><td>TS-DDEO</td><td>SA-MPSO</td><td>SAMFEO</td><td>GL-SADE</td><td>ESA</td><td>AutoSAEA</td></tr><tr><td>+/=/-</td><td>5/8/2</td><td>6/6/3</td><td>12/2/1</td><td>12/3/0</td><td>9/3/3</td><td>9/4/2</td><td>12/2/1</td><td>6/8/1</td><td>12/2/1</td><td>6/8/1</td><td>NA</td></tr><tr><td>Ranking</td><td>3.4</td><td>4.1</td><td>8.4</td><td>8.3</td><td>7.0</td><td>5.9</td><td>7.4</td><td>5.2</td><td>9.2</td><td>4.3</td><td>2.9</td></tr><tr><td>\\( p \\) -value</td><td>0.74</td><td>0.74</td><td>0.0001</td><td>0.0001</td><td>0.004</td><td>0.05</td><td>0.001</td><td>0.26</td><td>0</td><td>0.74</td><td>NA</td></tr></table>\n\n<!-- Media -->\n\n2) Compared With Multiple-Model SAEAs: While multiple-model SAEAs can leverage the advantages of different models and infill criteria, they cannot identify which ones are most suitable for a given problem. This often leads to high consumption of computational resources on ineffective models and infill criteria [23].\n\n3) Compared With Adaptive-Model SAEAs: Adaptive-model SAEAs aim to not only exploit the advantages of different models and infill criteria but also identify the most suitable ones for a given problem. However, the performance of adaptive SAEAs depends on the specific model and infill criterion configuration used, as well as the adaptive selection strategy. Compared to existing adaptive-model SAEAs, AutoSAEA not only enriches the model/infill criterion configuration but also adopts a more effective adaptive selection strategy (i.e., TL-MAB) to select them in a hierarchical, coupled way. Therefore, AutoSAEA can outperform existing state-of-the-art adaptive-model SAEAs.\n\nDue to page limitations, we have included the convergence profiles of all tested algorithms, as well as the computational complexity of AutoSAEA and running times of all compared algorithms, in the supplementary file.\n\n## C. Adaptive Behavior Analysis\n\nAutoSAEA formulates the surrogate model and infill criterion selection as a TL-MAB problem and designs a TL-R and a TL-UCB to address it. To demonstrate the effectiveness of the adaptive surrogate model and infill criterion selection used in AutoSAEA, we first compare it with eleven variants.\n\n1) V-CA1 to V-CA8: AutoSAEA uses the fixed combinatorial arms in \\( \\mathcal{{CA}} \\) during the entire optimization process. V-CA \\( i \\) denotes that it uses the \\( i \\) th \\( \\left( {i = 1,\\ldots ,8}\\right) \\) combinatorial arm.\n\n2) V-Random: The surrogate model and infill criterion are randomly selected.\n\n3) V-SLUCB: AutoSAEA uses a single-level UCB and a single-level reward to adaptively choose the combinatorial arms from \\( \\mathcal{{CA}} \\) .\n\n4) \\( V - Q \\) : AutoSAEA uses the \\( Q \\) -learning method [25] to adaptively select the combinatorial arms from \\( \\mathcal{{CA}} \\) in each iteration.\n\nThe experiments are conducted on the CEC2005 10D and 30D problems, with all experimental setups kept the same as in the previous section. The mean and standard deviation of the function error value for each variant are provided in Tables V and VI of the supplementary file, and the statistical results are shown in Table II.\n\nFor the CEC2005 10D problems, the Wilcoxon rank-sum test indicates that AutoSAEA significantly outperforms its variants. Specifically, it outperforms or competes with all variants on all problems, except that it is worse than VCA-5 and V-Q on one problem each out of 15. Overall, AutoSAEA achieves the best ranking and significantly outperforms VCA- 2, VCA-4, VCA-5, VCA-6, VCA-7, and VCA-8 according to the Friedman test.\n\nFor the CEC2005 30D problems, based on the Wilcoxon rank-sum test, AutoSAEA is significantly better than all variants in most cases, and it is only outperformed by V-CA1, V-CA2, VCA-5, V-CA7, V-Random, and V-Q on 2, 2, 2, 2, 2, and 2 out of 15 problems, respectively.\n\nWhile AutoSAEA exhibits remarkable advantages over some of its variants, such as V-CA4, V-CA5, V-CA6, V-CA7, and V-CA8, it is worth noting that there is no significant difference between AutoSAEA and some of its variants, namely, V-CA1, V-CA2, V-CA3, V-Random, V-SLUCB, and V-Q. This indicates that AutoSAEA does not have advantages over its variants on some problems. The reasons behind this are provided as follows, based on the detailed analysis of the results.\n\n1) For some problems,such as CEC2005 F8 \\( \\left( {D = {10}}\\right) \\) , CEC2005 F15 \\( \\left( {D = {10}}\\right) \\) ,CEC2005 F8 \\( \\left( {D = {30}}\\right) \\) , CEC2005 F11 (D = 30),and CEC2005 F14 (D = 30), no models and infill criteria work effectively, so AutoSAEA cannot address them well.\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 9, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: XIE et al.: SAEA 1123-->\n\n<!-- Media -->\n\nTABLE II\n\nCOMPARISON RESULTS BETWEEN AUTOSAEA AND ITS VARIANTS AND THEIR RANKING ON CEC2005 10D AND 30D PROBLEMS\n\n<table><tr><td colspan=\"13\">CEC2005 10D problems</td></tr><tr><td/><td>V-CA1</td><td>V-CA2</td><td>V-CA3</td><td>V-CA4</td><td>V-CA5</td><td>V-CA6</td><td>V-CA7</td><td>V-CA8</td><td>V-Random</td><td>V-SLUCB</td><td>V-Q</td><td>AutoSAEA</td></tr><tr><td>\\( + / = 1 - \\)</td><td>8/7/0</td><td>9/6/0</td><td>8/7/0</td><td>14/1/0</td><td>\\( {13}/1/1 \\)</td><td>\\( {14}/1/0 \\)</td><td>13/2/0</td><td>\\( {14}/1/0 \\)</td><td>8/7/0</td><td>2/13/0</td><td>11/3/1</td><td>NA</td></tr><tr><td>Ranking</td><td>5.4</td><td>6.6</td><td>5.3</td><td>9.1</td><td>7.3</td><td>8.9</td><td>9.5</td><td>10.9</td><td>4.0</td><td>3.1</td><td>5.1</td><td>2.5</td></tr><tr><td>\\( p \\) -value</td><td>0.13</td><td>0.01</td><td>0.13</td><td>0</td><td>0.002</td><td>0</td><td>0</td><td>0</td><td>0.47</td><td>0.61</td><td>0.13</td><td>NA</td></tr><tr><td colspan=\"13\">CEC2005 30D problems</td></tr><tr><td/><td>V-CA1</td><td>V-CA2</td><td>V-CA3</td><td>V-CA4</td><td>V-CA5</td><td>V-CA6</td><td>V-CA7</td><td>V-CA8</td><td>V-Random</td><td>V-SLUCB</td><td>V-O</td><td>AutoSAEA</td></tr><tr><td>\\( + / = / - \\)</td><td>7/6/2</td><td>8/5/2</td><td>8/7/0</td><td>11/4/0</td><td>11/2/2</td><td>\\( {13}/2/0 \\)</td><td>11/2/2</td><td>13/2/0</td><td>9/4/2</td><td>4/11/0</td><td>9/4/2</td><td>NA</td></tr><tr><td>Ranking</td><td>4.2</td><td>4.9</td><td>5.5</td><td>8.5</td><td>8.5</td><td>11.3</td><td>8.2</td><td>10.7</td><td>4.8</td><td>3.5</td><td>6.1</td><td>2.6</td></tr><tr><td>\\( p \\) -value</td><td>0.41</td><td>0.38</td><td>0.18</td><td>0</td><td>0</td><td>0</td><td>0.0001</td><td>0</td><td>0.38</td><td>0.51</td><td>0.003</td><td>NA</td></tr></table>\n\n<!-- Media -->\n\n2) For some problems,such as CEC2005 F9 \\( \\left( {D = {10}}\\right) \\) , CEC2005 F10 \\( \\left( {D = {10}}\\right) , \\) CEC2005 F12 \\( \\left( {D = {10}}\\right) \\) , CEC2005 F2 \\( \\left( {D = {30}}\\right) \\) ,and CEC2005 F3 \\( \\left( {D = {30}}\\right) \\) , they can be addressed well by various models and infill criteria, so AutoSAEA does not have significant advantages over its some variants on these problems.\n\nTo visually demonstrate the adaptive behavior of our proposed AutoSAEA, we show the selected high-level arm (surrogate model) and low-level arm (infill criterion) during the optimization process of AutoSAEA in solving four representative problems,namely,CEC2005 F4 \\( \\left( {D = {10}}\\right) \\) ,CEC2005 F10 \\( \\left( {D = {10}}\\right) ,\\mathrm{{CEC}}{2005}\\mathrm{\\;F}2\\left( {D = {30}}\\right) \\) ,and \\( \\mathrm{{CEC}}{2005}\\mathrm{\\;F}{12} \\) \\( \\left( {D = {30}}\\right) \\) ,in Fig. 4. Additionally,we count the number of times each surrogate model and infill criterion is chosen in Fig. 5. From Figs. 4 and 5, we observe the following.\n\n1) For CEC2005 F4 \\( \\left( {D = {10}}\\right) \\) ,the PRS with prescreen-ing is selected most frequently. This is because this problem is unimodal, its landscape is smooth, and its dimensionality is small, making PRS a good choice for approximating this problem.\n\n2) For CEC2005 F10 \\( \\left( {D = {10}}\\right) \\) ,the algorithm prefers different models at different optimization stages. Specifically, the whole optimization procedure can be roughly divided into five stages, and GP, KNN, RBF, PRS, and KNN are, respectively, selected most frequently.\n\n3) For CEC2005 F2 \\( \\left( {D = {30}}\\right) \\) ,the GP model with EI and LCB is selected most frequently, especially in later stages. Interestingly, GP always combines LCB when \\( {650} < \\) FEs \\( < {750} \\) .\n\n4) For CEC2005 F12 (D = 30),GP with LCB or EI is mainly chosen when \\( {100} < {FEs} < {350} \\) . However,when \\( {350} < \\) FEs \\( < {550} \\) ,the RBF model with prescreening or local search is preferred, and the GP model is no longer selected. When \\( {550} < {FEs} < {1000} \\) ,RBF and GP are the most and second most commonly chosen models, respectively.\n\nIn summary, for different problems and different optimization stages, the selected surrogate model and infill criterion are different. Therefore, combined with the outstanding performance, the effectiveness of the surrogate model and infill criterion auto-configuration in AutoSAEA has been verified.\n\n### D.Oil Reservoir Production Optimization Problem\n\nAutoSAEA is applied to an oil reservoir production problem in the section to demonstrate its advantage further. This production optimization problem aims to find the optimal control parameters for the water-injection-rate of injection wells and fluid-production-rate of production wells in the expected life to maximize the net present value (NPV). In general, the oil reservoir production optimization problem can be expressed in the following form [60]:\n\n\\[\\max \\mathop{\\sum }\\limits_{{t = 1}}^{T}{\\Delta }_{t}{r}_{o}{Q}_{o,t}\\left( \\mathbf{x}\\right)  - {r}_{w}{Q}_{w,t}\\left( \\mathbf{x}\\right)  - {r}_{i}{Q}_{i,t}\\left( \\mathbf{x}\\right) \\]\n\n\\[\\text{s.t.}0 \\leq  {x}_{i,t} \\leq  {500},i = 1,\\ldots ,8,t = 1,\\ldots ,5 \\tag{31}\\]\n\n<!-- Media -->\n\nTABLE III\n\nRESULT OF GP-LCB, GP-EI, IKAEA, ESAO, TS-DDEO, SA-MPSO, GL-SADE, ESA, AND AUTOSAEA ON THE OIL RESERVOIR Production Optimization Problem. The Unit of Time Is Seconds\n\n<table><tr><td>Algorithm</td><td>Mean(std)</td><td>Median</td><td>Worst</td><td>Best</td><td>Time</td></tr><tr><td>GP-LCB</td><td>\\( {1.34}\\mathrm{e} + {04}\\left( {{2.2}\\mathrm{e} + {02}}\\right)  = \\)</td><td>1.35e+04</td><td>\\( {1.31e} + {00} \\)</td><td>1.36e+04</td><td>\\( {2.58}\\mathrm{e} + {05} \\)</td></tr><tr><td>GP-EI</td><td>\\( {1.34}\\mathrm{e} + {04}\\left( {{1.7}\\mathrm{e} + {02}}\\right)  = \\)</td><td>\\( {1.35}\\mathrm{e} + {04} \\)</td><td>1.32e+04</td><td>1.36e+04</td><td>\\( {2.18e} + {05} \\)</td></tr><tr><td>IKAEA</td><td>1.28e+04(1.9e+02)+</td><td>\\( {1.27}\\mathrm{e} + {04} \\)</td><td>1.26e+04</td><td>\\( {1.31}\\mathrm{e} + {04} \\)</td><td>\\( {2.45e} + {05} \\)</td></tr><tr><td>ESAO</td><td>1.27e+04(6.4e+02)+</td><td>\\( {1.30}\\mathrm{e} + {04} \\)</td><td>\\( {1.16e} + {04} \\)</td><td>1.32e+04</td><td>\\( {2.64}\\mathrm{e} + {05} \\)</td></tr><tr><td>TS-DDEO</td><td>\\( {1.34}\\mathrm{e} + {04}\\left( {{1.7}\\mathrm{e} + {02}}\\right)  = \\)</td><td>1.34e+04</td><td>1.31e+04</td><td>1.36e+04</td><td>\\( {3.04}\\mathrm{e} + {05} \\)</td></tr><tr><td>SA-MPSO</td><td>1.13e+04(1.3e+02)+</td><td>\\( {1.13}\\mathrm{e} + {04} \\)</td><td>\\( {1.12}\\mathrm{e} + {04} \\)</td><td>\\( {1.15}\\mathrm{e} + {04} \\)</td><td>\\( {3.52}\\mathrm{e} + {05} \\)</td></tr><tr><td>GL-SADE</td><td>\\( {1.31e} + {04}\\left( {{3.8e} + {02}}\\right)  = \\)</td><td>\\( {1.33}\\mathrm{e} + {04} \\)</td><td>\\( {1.28e} + {04} \\)</td><td>\\( {1.35}\\mathrm{e} + {04} \\)</td><td>\\( {3.48}\\mathrm{e} + {05} \\)</td></tr><tr><td>ESA</td><td>\\( {1.34}\\mathrm{e} + {04}\\left( {{1.7}\\mathrm{e} + {02}}\\right)  = \\)</td><td>\\( {1.33}\\mathrm{e} + {04} \\)</td><td>1.33e+04</td><td>1.37e+04</td><td>\\( {2.98}\\mathrm{e} + {05} \\)</td></tr><tr><td>AutoSAEA</td><td>1.36e+04(3.0e+02)</td><td>\\( {1.35}\\mathrm{e} + {04} \\)</td><td>1.32e+04</td><td>1.41e+04</td><td>\\( {2.13}\\mathrm{e} + {05} \\)</td></tr></table>\n\n<!-- Media -->\n\nwhere \\( \\mathbf{x} = {\\left( {x}_{1,1},\\ldots ,{x}_{1,5},\\ldots ,{x}_{8,1},\\ldots ,{x}_{8,5}\\right) }^{\\top } \\) is the decision vector,and \\( {x}_{i,t} \\) denotes the flow rate of the \\( i \\) th well at the \\( t \\) -th time step. Therefore,40 variables need to be optimized. The lower and upper bounds of each decision variable are set to \\( 0\\mathrm{{STB}}/ \\) day and \\( {500}\\mathrm{{STB}}/ \\) day,respectively. \\( T = 5 \\) denotes the number of time steps. \\( {\\Delta }_{t} = {720} \\) is the time of the \\( t \\) -th time step. \\( {r}_{o},{r}_{w} \\) ,and \\( {r}_{i} \\) ,respectively,denote the oil-production revenue, water-production cost, and water-injection cost, which are set to 20 USD/STB, 1 USD/STB, and 3 USD/STB,respectively. \\( {Q}_{o,t},{Q}_{w,t} \\) ,and \\( {Q}_{i,t} \\) denote the oil-production rate, water-production rate, and injection-flow-rate at the \\( t \\) -th time step,respectively,which are calculated by a numerical simulator. In this case, the Egg model [61], including eight water-injection wells and four production wells, is selected as the reservoir simulator. More information about the Egg model can be found in [61]. In this article, the MRST toolbox [62] is adopted to conduct the Egg model. Note that it takes about \\( {40}\\mathrm{\\;s} \\) to evaluate one solution using the simulator.\n\nTo demonstrate the performance of AutoSAEA in solving the oil reservoir production optimization problem, eight existing SAEAs are used to test. To make a fair comparison, the initial number of solutions and the total number of FEs for all algorithms are set to 100 and 1000, respectively. Moreover, each algorithm conducts five independent runs.\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 10, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: 1124 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->\n\n<!-- Media -->\n\n<!-- figureText: F4 \\( \\left( {D = {10}}\\right) \\) \\( \\mathrm{F}{10}\\left( {D = {10}}\\right) \\) GP RBF KNN Prescreening (RBF) ( \"2000 ) (   ) (   ) ) (   ) (   ) (   ) ) (   ) ) (   ) ) (   ) ) Local search (RBF) CIRITIONITION Prescreening (PRS) Local search (PRS) L1-exploration 0 100 200 300 400 500 600 800 900 1000 FEs F \\( {12}\\left( {D = {30}}\\right) \\) GP RBF PRS KNN O O O O O O O O O O O O O O O O O LCB _____ Prescreening (RBF) CONTITION CONTION Local search (RBF) ④ ② C D Prescreening (PRS) 课文： O O O O Local search (PRS) ③O O O O L1-exploitation GITO CO COMO COM O O O O O O O O O O O O O O O O O 300 400 500 900 100 RBF 60 High-level Prescreening (RBF) CO Local search (RBF) Prescreening (PRS) Local search (PRS) L1-exploration - D \\{ 100 200 300 400 500 600 700 800 900 1000 \\( \\mathrm{F}2\\left( {D = {30}}\\right) \\) GP PRS High-leve KNN ORMO NOMO O O O O O O O O O O O O O O O LCB Prescreening (RBF ORIDOOOOOOOO 00000 Local search (RBF) OO O O O Prescreening (PRS) Local search (PRS) ③ ① ② ① ② (   ) L1-exploitation 202012年11月11日(2000年) 0 100 400 500 600 1000 -->\n\n<img src=\"./images/image_3_f5efad6619ec0069d23b4422c9107014.jpg\"/>\n\nFig. 4. Selected surrogate model and infill criterion during the optimization process of AutoSAEA on CEC2005 F4 \\( \\left( {D = {10}}\\right) \\) ,CEC2005 F10 \\( \\left( {D = {10}}\\right) \\) , CEC2005 F2 \\( \\left( {D = {30}}\\right) \\) ,and CEC2005 F12 \\( \\left( {D = {30}}\\right) \\) .\n\n<!-- figureText: 700 - GP 2 RBF 3 PRS DIKNN 3LCB -1.E1 Prescreening (RBF) & Local search (RBF) Prescreening (PRS) - L1-exploitation L1-exploration CEC2005 F2 (30D) CEC2005 F12 (30D) 600 500 200 145 100 3535 CEC2005 F4 (10D) CEC2005 F10 (10D) -->\n\n<img src=\"./images/image_4_72fe45fea6dc0b658582ba9383217941.jpg\"/>\n\nFig. 5. Number of times each surrogate model and infill criterion is chosen during the optimization process of AutoSAEA on CEC2005 F4 ( \\( D = {10} \\) ), CEC2005 F10 \\( \\left( {D = {10}}\\right) \\) ,CEC2005 F2 \\( \\left( {D = {30}}\\right) \\) ,and CEC2005 F12 \\( \\left( {D = {30}}\\right) \\) .\n\n<!-- figureText: 14000 GP-EI IKAEA ESAO TS-DDEO SA-MPSO ESA AutoSAEA 600 800 1000 FEs 12000 10000 NPV 8000 6000 2000 0 200 400 -->\n\n<img src=\"./images/image_5_8f2349033e42e4e1bd7a8918670babdf.jpg\"/>\n\nFig. 6. Average convergence profile of the median NPV value and its interquartile ranges of GP-LCB, GP-EI, IKAEA, ESAO, TS-DDEO, SA-MPSO, GL-SADE, ESA, and AutoSAEA on the oil reservoir production optimization problem.\n\n<!-- Media -->\n\nThe statistical results are provided in Table III. It can be seen that AutoSAEA can get the best average performance. Based on the Wilcoxon rank-sum test, AutoSAEA is significantly better than IKAEA, ESAO, and SA-MPSO and performs competitively with GP-LCB, GP-EI, ESA, TS-DDEO, and GL-SADE. Moreover, the running time of AutoSAEA is also the lowest, which shows the better computational efficiency of AutoSAEA compared with its competitors. Besides, the average convergence performance of each algorithm on this problem is plotted in Fig. 6, from which we can find that AutoSAEA also has some advantages in solving this oil reservoir production optimization problem.\n\n## VI. CONCLUSION\n\nThe surrogate model and infill criterion are vital to the performance of SAEAs. To enhance the ability of SAEA to solve various EOPs, this article proposes a TL-MAB to cooperatively choose the surrogate model and infill criterion in an online manner. To achieve this, a TL-R mechanism is defined to measure the optimization utility of the surrogate model and infill criterion in a hierarchical and coupled manner. Additionally, a TL-UCB strategy is designed to choose them cooperatively and adaptively. With these adaptive selection components, an auto SAEA has been proposed, called AutoSAEA. The performance of AutoSAEA has been demonstrated by comparing it with several state-of-the-art SAEAs on two sets of benchmark problems (CEC2005 and CEC2015) and a real-world oil reservoir production optimization problem. Furthermore, the adaptive behavior of AutoSAEA has been numerically verified, and the sensitivity of its control parameters has been analyzed.\n\nIn the future, we plan to extend the adaptive surrogate model and infill criterion selection method to expensive multiobjective optimization. Moreover, we will use the proposed AutoSAEA to tackle other real-world EOPs. REFERENCES\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 11, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: XIE et al.: SAEA 1125-->\n\n[1] <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, \"Progress in design optimization using evolutionary algorithms for aerodynamic problems,\" Progr. Aerosp. Sci., vol. 46, nos. 5-6, pp. 199-223, 2010.\n\n[2] <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, \"Approximation methods in multidisciplinary analysis and optimization: A panel discussion,\" Struct. Multidiscipl. Optim., vol. 27, no. 5, pp. 302-313, 2004.\n\n[3] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, \"Optimal design of passive control of space tethered-net capture system,\" IEEE Access, vol. 7, pp. 131383-131394, 2019.\n\n[4] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, \"A generator for multiobjective test problems with difficult-to-approximate Pareto front boundaries,\" IEEE Trans. Evol. Comput., vol. 23, no. 4, pp. 556-571, Aug. 2019.\n\n[5] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, \"Solving expensive multimodal optimization problem by a decomposition differential evolution algorithm,\" IEEE Trans. Cybern., vol. 53, no. 4, pp. 2236-2246, Apr. 2023.\n\n[6] G. Li and Q. Zhang, \"Multiple penalties and multiple local surrogates for expensive constrained optimization,\" IEEE Trans. Evol. Comput., vol. 25, no. 4, pp. 769-778, Aug. 2021.\n\n[7] G. Li, L. Xie, Z. <PERSON>, H. Wang, and M. Gong, \"Evolutionary algorithm with individual-distribution search strategy and regression-classification surrogates for expensive optimization,\" Inf. Sci., vol. 634, pp. 423-442, Jul. 2023.\n\n[8] H. Wang, H. Xu, and Z. Zhang, \"High-dimensional multi-objective Bayesian optimization with block coordinate updates: Case studies in intelligent transportation system,\" IEEE Trans. Intell. Transp. Syst., early access, Feb. 7, 2023, doi: 10.1109/TITS.2023.3241069.\n\n[9] T. Goel, R. T. Hafkta, and W. Shyy, \"Comparing error estimation measures for polynomial and kriging approximation of noise-free functions,\" Struct. Multidiscip. Optim., vol. 38, no. 5, pp. 429-442, 2009.\n\n[10] L. M. Zouhal and T. Denoeux, \"An evidence-theoretic k-NN rule with parameter optimization,\" IEEE Trans. Syst., Man, Cybern. C, Appl. Rev., vol. 28, no. 2, pp. 263-271, Mar. 1998.\n\n[11] S. M. Clarke, J. H. Griebsch, and T. W. Simpson, \"Analysis of support vector regression for approximation of complex engineering analyses,\" \\( J \\) . Mech. Design, vol. 127, no. 6, pp. 1077-1087, 2005.\n\n[12] Y. Jin, M. Olhofer, and B. Sendhoff, \"A framework for evolutionary optimization with approximate fitness functions,\" IEEE Trans. Evol. Comput., vol. 6, no. 5, pp. 481-494, Oct. 2002.\n\n[13] B. Liu, Q. Zhang, and G. G. E. Gielen, \"A Gaussian process surrogate model assisted evolutionary algorithm for medium scale expensive optimization problems,\" IEEE Trans. Evol. Comput., vol. 18, no. 2, pp. 180-192, Apr. 2014.\n\n[14] J. Tian, Y. Tan, J. Zeng, C. Sun, and Y. Jin, \"Multiobjective infill criterion driven Gaussian process-assisted particle swarm optimization of high-dimensional expensive problems,\" IEEE Trans. Evol. Comput., vol. 23, no. 3, pp. 459-472, Jul. 2019.\n\n[15] F. Li, X. Cai, L. Gao, and W. Shen, \"A surrogate-assisted multiswarm optimization algorithm for high-dimensional computationally expensive problems,\" IEEE Trans. Cybern., vol. 51, no. 3, pp. 1390-1402, Mar. 2021.\n\n[16] Y. Liu, J. Liu, and Y. Jin, \"Surrogate-assisted multipopulation particle swarm optimizer for high-dimensional expensive optimization,\" IEEE Trans. Syst., Man, Cybern., Syst., vol. 52, no. 7, pp. 4671-4684, Jul. 2022.\n\n[17] J. Liu, Y. Wang, G. Sun, and T. Pang, \"Multisurrogate-assisted ant colony optimization for expensive optimization problems with continuous and categorical variables,\" IEEE Trans. Cybern., vol. 52, no. 11, pp. 11348-11361, Nov. 2022.\n\n[18] W. Wang, H.-L. Liu, and K. C. Tan, \"A surrogate-assisted differential evolution algorithm for high-dimensional expensive optimization problems,\" IEEE Trans. Cybern., vol. 53, no. 4, pp. 2685-2697, Apr. 2023.\n\n[19] H. Wang, Y. Jin, and J. Doherty, \"Committee-based active learning for surrogate-assisted particle swarm optimization of expensive problems,\" IEEE Trans. Cybern., vol. 47, no. 9, pp. 2664-2677, Sep. 2017.\n\n[20] T. Sonoda and M. Nakata, \"Multiple classifiers-assisted evolutionary algorithm based on decomposition for high-dimensional multi-objective problems,\" IEEE Trans. Evol. Comput., vol. 26, no. 6, pp. 1581-1595, Dec. 2022.\n\n[21] X. Wu, Q. Lin, J. Li, K. C. Tan, and V. C. Leung, \"An ensemble surrogate-based coevolutionary algorithm for solving large-scale expensive optimization problems,\" IEEE Trans. Cybern., early access, Sep. 16, 2022, doi: 10.1109/TCYB.2022.3200517.\n\n[22] M. W. Hoffman, E. Brochu, and N. De Freitas, \"Portfolio allocation for Bayesian optimization,\" in Proc. UAI, 2011, pp. 327-336.\n\n[23] Z. Song, H. Wang, C. He, and Y. Jin, \"A Kriging-assisted two-archive evolutionary algorithm for expensive many-objective optimization,\" IEEE Trans. Evol. Comput., vol. 25, no. 6, pp. 1013-1027, Dec. 2021.\n\n[24] Z. Liu, H. Wang, and Y. Jin, \"Performance indicator-based adaptive model selection for offline data-driven multiobjective evolutionary optimization,\" IEEE Trans. Cybern., early access, May 13, 2022, doi: 10.1109/TCYB.2022.3170344.\n\n[25] H. Zhen, W. Gong, and L. Wang, \"Evolutionary sampling agent for expensive problems,\" IEEE Trans. Evol. Comput., vol. 27, no. 3, pp. 716-727, Jun. 2023.\n\n[26] Q. Zhang, W. Liu, E. Tsang, and B. Virginas, \"Expensive multiobjective optimization by MOEA/D with Gaussian process model,\" IEEE Trans. Evol. Comput., vol. 14, no. 3, pp. 456-474, Jun. 2010.\n\n[27] J. E. Dennis and V. Torczon, \"Managing approximation models in optimization,\" Multidiscip. Design Optim. State-Art, vol. 5, pp. 330-347, Nov. 1998.\n\n[28] G. Li, Q. Zhang, Q. Lin, and W. Gao, \"A three-level radial basis function method for expensive optimization,\" IEEE Trans. Cybern., vol. 52, no. 7, pp. 5720-5731, Jul. 2022.\n\n[29] J. Zhang, A. Zhou, and G. Zhang, \"A multiobjective evolutionary algorithm based on decomposition and preselection,\" in Bio-Inspired Computing-Theories and Applications. Heidelberg, Germany: Springer, 2015, pp. 631-642.\n\n[30] M. Yu, X. Li, and J. Liang, \"A dynamic surrogate-assisted evolutionary algorithm framework for expensive structural optimization,\" Struct. Multidiscip. Optim., vol. 61, no. 2, pp. 711-729, 2020.\n\n[31] H. Zhen, W. Gong, L. Wang, F. Ming, and Z. Liao, \"Two-stage data-driven evolutionary optimization for high-dimensional expensive problems,\" IEEE Trans. Cybern., vol. 53, no. 4, pp. 2368-2379, Apr. 2023.\n\n[32] X. Wang, G. G. Wang, B. Song, P. Wang, and Y. Wang, \"A novel evolutionary sampling assisted optimization method for high-dimensional expensive problems,\" IEEE Trans. Evol. Comput., vol. 23, no. 5, pp. 815-827, Oct. 2019.\n\n[33] X. Cai, L. Gao, and X. Li, \"Efficient generalized surrogate-assisted evolutionary algorithm for high-dimensional expensive problems,\" IEEE Trans. Evol. Comput., vol. 24, no. 2, pp. 365-379, Apr. 2020.\n\n[34] Z. Wang et al., \"Multiobjective optimization-aided decision-making system for large-scale manufacturing planning,\" IEEE Trans. Cybern., vol. 52, no. 8, pp. 8326-8339, Aug. 2022.\n\n[35] D. Guo, Y. Jin, J. Ding, and T. Chai, \"Heterogeneous ensemble-based infill criterion for evolutionary multiobjective optimization of expensive problems,\" IEEE Trans. Cybern., vol. 49, no. 3, pp. 1012-1025, Mar. 2019.\n\n[36] J.-Y. Li, Z.-H. Zhan, H. Wang, and J. Zhang, \"Data-driven evolutionary algorithm with perturbation-based ensemble surrogates,\" IEEE Trans. Cybern., vol. 51, no. 8, pp. 3925-3937, Aug. 2021.\n\n[37] L. Kocsis and C. Szepesvári, \"Bandit based Monte-Carlo planning,\" in Proc. Eur. Conf. Mach. Learn., 2006, pp. 282-293.\n\n[38] S. Pandey, D. Chakrabarti, and D. Agarwal, \"Multi-armed bandit problems with dependent arms,\" in Proc. 24th Int. Conf. Mach. Learn., 2007, pp. 721-728.\n\n[39] E. Carlsson, D. Dubhashi, and F. D. Johansson, \"Thompson sampling for bandits with clustered arms,\" 2021, arXiv:2109.01656.\n\n[40] D. R. Jones, M. Schonlau, and W. J. Welch, \"Efficient global optimization of expensive black-box functions,\" J. Global Optim., vol. 13, no. 4, p. 455, 1998.\n\n[41] P. I. Frazier, \"A tutorial on Bayesian optimization,\" 2018, arXiv:1807.02811.\n\n[42] D. Zhan and H. Xing, \"A fast Kriging-assisted evolutionary algorithm based on incremental learning,\" IEEE Trans. Evol. Comput., vol. 25, no. 5, pp. 941-955, Oct. 2021.\n\n[43] D. D. Cox and S. John, \"A statistical method for global optimization,\" in Proc. IEEE Int. Conf. Syst., Man, Cybern., 1992, pp. 1241-1246.\n\n[44] J. Zhang, A. Zhou, and G. Zhang, \"A classification and Pareto domination based multiobjective evolutionary algorithm,\" in Proc. IEEE Congr. Evol. Comput. (CEC), 2015, pp. 2883-2890.\n\n[45] C. Sun, Y. Jin, R. Cheng, J. Ding, and J. Zeng, \"Surrogate-assisted cooperative swarm optimization of high-dimensional expensive problems,\" IEEE Trans. Evol. Comput., vol. 21, no. 4, pp. 644-660, Aug. 2017.\n\n[46] Y. Haibo, T. Ying, Z. Jianchao, S. Chaoli, and J. Yaochu, \"Surrogate-assisted hierarchical particle swarm optimization,\" Inf. Sci., vols. 454-455, pp. 59-72, Jul. 2018.\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}, {"url": "", "pageIdx": 12, "pageWidth": 1802, "pageHeight": 2332, "md": "\n\n<!-- Meanless: 1126 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->\n\n[47] F.<PERSON><PERSON><PERSON> et al., \"A classifier-assisted level-based learning swarm optimizer for expensive optimization,\" IEEE Trans. Evol. Comput., vol. 25, no. 2, pp. 219-233, Apr. 2020.\n\n[48] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, \"Reference vector-assisted adaptive model management for surrogate-assisted many-objective optimization,\" IEEE Trans. Syst., Man, Cybern., Syst., vol. 52, no. 12, pp. 7760-7773, Dec. 2022.\n\n[49] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, \"Radial basis function assisted optimization method with batch infill sampling criterion for expensive optimization,\" in Proc. IEEE Congr. Evol. Comput. (CEC), 2019, pp. 1664-1671.\n\n[50] <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON>, \"Response surface methodology,\" Wiley Interdiscipl. Rev. Comput. Stat., vol. 2, no. 2, pp. 128-149, 2010.\n\n[51] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, \"Solving nonlinear equation systems by a two-phase evolutionary algorithm,\" IEEE Trans. Syst., <PERSON>, <PERSON><PERSON>n., <PERSON>ys<PERSON>., vol. 51, no. 9, pp. 5652-5663, Sep. 2021.\n\n[52] L. Pan, C. He, Y. Tian, H. <PERSON>, X. Zhang, and Y. <PERSON>, \"A classification-based surrogate-assisted evolutionary algorithm for expensive many-objective optimization,\" IEEE Trans. <PERSON><PERSON>. Comput., vol. 23, no. 1, pp. 74-88, Feb. 2019.\n\n[53] T. <PERSON>, M. Li, and M. Poloczek, \"Fast reconfigurable antenna state selection with hierarchical Thompson sampling,\" in Proc. IEEE Int. Conf. Commun. (ICC), 2019, pp. 1-6.\n\n[54] R. Singh, F. Liu, Y. Sun, and N. Shroff, \"Multi-armed bandits with dependent arms,\" 2020, arXiv:2010.09478.\n\n[55] J. Hong, B. Kveton, M. Zaheer, and M. Ghavamzadeh, \"Hierarchical Bayesian bandits,\" in Proc. Int. Conf. Artif. Intell. Stat., 2022, pp. 7724-7741.\n\n[56] P. N. Suganthan et al., \"Problem definitions and evaluation criteria for the CEC 2005 special session on real-parameter optimization,\" Nanyang Technol. Univ., Singapore, IIT, Kanpur, India, KanGAL Rep. #2005005, 2005.\n\n[57] J. Liang, B. Qu, P. Suganthan, and Q. Chen, \"Problem definitions and evaluation criteria for the CEC 2015 competition on learning-based real-parameter single objective optimization,\" Comput. Intell. Lab., Nanyang Technol. Univ., Singapore, Rep. 201411A, 2014.\n\n[58] G. Li, Z. Wang, and M. Gong, \"Expensive optimization via surrogate-assisted and model-free evolutionary optimization,\" IEEE Trans. Syst., Man, Cybern., Syst., vol. 53, no. 5, pp. 2758-2769, May 2023.\n\n[59] Z. Wang, Y.-S. Ong, and H. Ishibuchi, \"On scalable multiobjective test problems with hardly dominated boundaries,\" IEEE Trans. Evol. Comput., vol. 23, no. 2, pp. 217-231, Apr. 2019.\n\n[60] G. Chen, X. Luo, J. J. Jiao, and X. Xue, \"Data-driven evolutionary algorithm for oil reservoir well-placement and control optimization,\" Fuel, vol. 326, Oct. 2022, Art. no. 125125.\n\n[61] J.-D. Jansen, R.-M. Fonseca, S. Kahrobaei, M. Siraj, G. Van Essen, and P. Van den Hof, \"The egg model-A geological ensemble for reservoir simulation,\" Geosci. Data J., vol. 1, no. 2, pp. 192-195, 2014.\n\n[62] K.-A. Lie, An Introduction to Reservoir Simulation Using MATLAB/GNU Octave: User Guide for the MATLAB Reservoir Simulation Toolbox (MRST). Cambridge, U.K.: Cambridge Univ. Press, 2019.\n\n<!-- Media -->\n\n<img src=\"./images/image_6_2a8299e3699679740472f917cbbfa94f.jpg\"/>\n\n<!-- Media -->\n\nLindong Xie received the B.S. degree in mechanical and electronic engineering from Henan University of Science and Technology, Luoyang, China, in 2021. He is currently pursuing the M.S. degree with the School of System Design and Intelligent Manufacturing, Southern University of Science and Technology, Shenzhen, China.\n\nHis current research interests include data-driven evolutionary algorithms, and machine learning and their applications.\n\n<!-- Media -->\n\n<img src=\"./images/image_7_c1e79fe334cd29533d6928af39361302.jpg\"/>\n\n<!-- Media -->\n\nGenghui Li received the M.Sc. degree in computer science and technology from Shenzhen University, Shenzhen, China, in 2016, and the Ph.D. degree in computer science from the City University of Hong Kong, Hong Kong, China, in 2021.\n\nHe is currently a Postdoctoral Fellow with the Southern University of Science and Technology, Shenzhen. His research interests include evolutionary computation, computational intelligence, and machine learning.\n\n<!-- Media -->\n\n<img src=\"./images/image_8_98e08e9712fc53f7be9ce71b05aa5e50.jpg\"/>\n\n<!-- Media -->\n\nZhenkun Wang (Member, IEEE) received the Ph.D. degree in circuits and systems from Xidian University, Xi'an, China, in 2016.\n\nFrom 2017 to 2020, he was a Postdoctoral Research Fellow with the School of Computer Science and Engineering, Nanyang Technological University, Singapore, and with the Department of Computer Science, City University of Hong Kong, Hong Kong. He is currently an Assistant Professor with the School of System Design and Intelligent Manufacturing and the Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen, China. His research interests include evolutionary computation, optimization, machine learning, and their applications.\n\nDr. Wang is an Associate Editor of the Swarm and Evolutionary Computation.\n\n<!-- Media -->\n\n<img src=\"./images/image_9_6cce093b4c81a95f2b38a8bb903e03fb.jpg\"/>\n\n<!-- Media -->\n\nLaizhong Cui (Senior Member, IEEE) received the B.S. degree from Jilin University, Changchun, China, in 2007, and the Ph.D. degree in computer science and technology from Tsinghua University, Beijing, China, in 2012.\n\nHe is currently a Professor with the College of Computer Science and Software Engineering, Shenzhen University, Shenzhen, China. He led more than ten scientific research projects, including National Key Research and Development Plan of China, National Natural Science Foundation of China, Guangdong Natural Science Foundation of China, and Shenzhen Basic Research Plan. He has published more than 100 papers, including IEEE JOURNAL ON SELECTED AREAS IN COMMUNICATIONS, IEBEE TRANSACTIONS ON COMPUTERS, IEEE TRANSACTIONS ON PARALLEL AND DISTRIBUTED SYSTEMS, IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, IEEE TRANSACTIONS ON MULTIMEDIA, IEEE INTERNET OF THINGS JOURNAL, IEEE TRANSACTIONS ON INDUSTRIAL INFORMATICS, IEEE TRANSACTIONS ON VEHICULAR TECHNOLOGY, IEEE TRANSACTIONS ON NETWORK AND SERVICE MANAGEMENT, ACM Transactions on Internet Technology, IEEE NETWORK, IEEE INFOCOM, ACM MM, IEEE ICNP, and IEEE ICDCS. His research interests include future Internet architecture and protocols, edge computing, multimedia systems and applications, Blockchain, Internet of Things, cloud computing, and federated learning.\n\nProf. Cui serves as an Associate Editor or a member of Editorial Board for several international journals, including IEEE INTERNET OF THINGS JOURNAL, IEEE TRANSACTIONS ON CLOUD COMPUTING, IEEE TRANSACTIONS ON NETWORK AND SERVICE MANAGEMENT, and International Journal of Machine Learning and Cybernetics. He is a Distinguished Member of the CCF.\n\n<!-- Media -->\n\n<img src=\"./images/image_10_f7e07d805e1e3a3d5835f44597fc4929.jpg\"/>\n\n<!-- Media -->\n\nMaoguo Gong (Senior Member, IEEE) received the B.Eng. degree (Hons.) in electronic engineering and Ph.D. degree in electronic science and technology from Xidian University, Xi'an, China, in 2003 and 2009, respectively.\n\nSince 2006, he has been a Teacher with Xidian University. He was promoted to an Associate Professor and a Full Professor in 2008 and 2010, respectively, both with exceptive admission. He is leading or has completed over 20 projects as the Principle Investigator, funded by the National Natural Science Foundation of China and the National Key Research and Development Program of China. He has published over 100 papers in journals and conferences, and holds over 20 granted patents as the first inventor. His research interests are broadly in the area of computational intelligence, with applications to optimization, learning, data mining, and image understanding.\n\nDr. Gong is the Director of the Chinese Association for Artificial Intelligence-Youth Branch, the Senior Member of Chinese Computer Federation, and an Associate Editor or an Editorial Board Member for over five journals, including the IEEE TRANSACTIONS ON NEURAL NETWORKS AND LEARNING SYSTEMS and the IEEE TRANSACTIONS ON EMERGING TOPICS IN COMPUTATIONAL INTELLIGENCE.\n\n<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->\n\n", "score": 0}]}