
                               ____                __         
            ____     __  __   / __/  ____ ___     / /_   ____ 
           / __ \   / / / /  / /_   / __ `__ \   / __/  / __ \
          / /_/ /  / /_/ /  / __/  / / / / / /  / /_   / /_/ /
         / .___/   \__, /  /_/    /_/ /_/ /_/   \__/   \____/ 
        /_/       /____/                                      

INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     1     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 10:44:50        launcher.py->line(169)|
╭───────────┬──────────┬───────────────┬─────────────┬───────────┬───────┬───────────┬────────╮
│  running  │  repeat  │   progress    │  algorithm  │  problem  │  iid  │  clients  │  save  │
├───────────┼──────────┼───────────────┼─────────────┼───────────┼───────┼───────────┼────────┤
│    1/1    │   1/20   │ [1/20][5.00%] │   IAFFBO    │ arxiv2017 │   2   │    18     │  True  │
╰───────────┴──────────┴───────────────┴─────────────┴───────────┴───────┴───────────┴────────╯
INFO    2025-08-05 10:44:50        launcher.py->line(196)|Server started.
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 1  started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 2  started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 3  started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 4  started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 5  started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 6  started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 7  started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 8  started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 9  started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 10 started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 11 started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 12 started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 13 started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 14 started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 15 started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 16 started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 17 started
INFO    2025-08-05 10:44:52   iaffbo_client.py->line(70)|Client 18 started
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 2  Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 1  Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 5  Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 4  Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 7  Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 6  Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 3  Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 9  Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 10 Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 11 Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 13 Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 8  Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 15 Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 12 Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 14 Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 17 Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 16 Connection refused 1 times.
ERROR   2025-08-05 10:44:53          client.py->line(205)|Client 18 Connection refused 1 times.
INFO    2025-08-05 10:44:54           tools.py->line(207)|
===================== IAFFBOServer ====================
╭───────────────────┬───────────┬───────────┬─────────╮
│ Parameter         │  Default  │  Updates  │  Using  │
├───────────────────┼───────────┼───────────┼─────────┤
│ batch_aggregation │     -     │   True    │    -    │
├───────────────────┼───────────┼───────────┼─────────┤
│ n_clusters        │     6     │     6     │    6    │
├───────────────────┼───────────┼───────────┼─────────┤
│ n_samples         │    100    │    100    │   100   │
├───────────────────┼───────────┼───────────┼─────────┤
│ device            │     -     │   cuda    │    -    │
├───────────────────┼───────────┼───────────┼─────────┤
│ agg_interval      │    0.3    │    0.3    │   0.3   │
╰───────────────────┴───────────┴───────────┴─────────╯
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 2  Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 2 join, total 1 clients
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 5  Connection refused 2 times.
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 1  Connection refused 2 times.
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 7  Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 5 join, total 2 clients
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 4  Connection refused 2 times.
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 6  Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 1 join, total 3 clients
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 7 join, total 4 clients
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 3  Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 4 join, total 5 clients
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 6 join, total 6 clients
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 9  Connection refused 2 times.
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 10 Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 3 join, total 7 clients
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 11 Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 10 join, total 8 clients
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 9 join, total 9 clients
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 11 join, total 10 clients
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 13 Connection refused 2 times.
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 8  Connection refused 2 times.
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 12 Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 13 join, total 11 clients
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 17 Connection refused 2 times.
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 14 Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 8 join, total 12 clients
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 12 join, total 13 clients
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 16 Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 17 join, total 14 clients
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 15 Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 14 join, total 15 clients
ERROR   2025-08-05 10:44:54          client.py->line(205)|Client 18 Connection refused 2 times.
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 15 join, total 16 clients
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 16 join, total 17 clients
INFO    2025-08-05 10:44:54          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:44:55          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:44:56          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:44:57          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:44:57          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:44:57          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:44:57          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:44:57          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:44:57          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:44:57          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:44:58          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:44:59          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:00          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:01          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:02          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:03          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:04          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:05          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:06          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:07          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:08          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:09          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:10          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:11          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 2 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 5 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 1 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 6 join, total 18 clients
INFO    2025-08-05 10:45:12          server.py->line(162)|Client 7 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 4 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 3 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 15 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 12 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 11 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 14 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 10 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 13 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 17 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 9 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 16 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 10:45:13          server.py->line(162)|Client 8 join, total 18 clients
INFO    2025-08-05 17:31:03           tools.py->line(207)|
=================== AutoSAEAClient ==================
╭─────────────────┬───────────┬───────────┬─────────╮
│ Parameter       │  Default  │  Updates  │  Using  │
├─────────────────┼───────────┼───────────┼─────────┤
│ sync_interval   │    10     │     5     │    5    │
├─────────────────┼───────────┼───────────┼─────────┤
│ population_size │    50     │    20     │   20    │
├─────────────────┼───────────┼───────────┼─────────┤
│ F               │    0.5    │    0.5    │   0.5   │
├─────────────────┼───────────┼───────────┼─────────┤
│ alpha           │     2     │     2     │    2    │
├─────────────────┼───────────┼───────────┼─────────┤
│ CR              │    0.9    │    0.9    │   0.9   │
╰─────────────────┴───────────┴───────────┴─────────╯
ERROR   2025-08-05 17:31:04          client.py->line(205)|Client 1  Connection refused 1 times.
ERROR   2025-08-05 17:31:05          client.py->line(205)|Client 1  Connection refused 2 times.
ERROR   2025-08-05 17:31:06          client.py->line(205)|Client 1  Connection refused 3 times.
ERROR   2025-08-05 17:31:07          client.py->line(205)|Client 1  Connection refused 1 times.
ERROR   2025-08-05 17:31:08          client.py->line(205)|Client 1  Connection refused 2 times.
ERROR   2025-08-05 17:31:09          client.py->line(205)|Client 1  Connection refused 3 times.
INFO    2025-08-05 17:31:49           tools.py->line(207)|
=================== AutoSAEAClient ==================
╭─────────────────┬───────────┬───────────┬─────────╮
│ Parameter       │  Default  │  Updates  │  Using  │
├─────────────────┼───────────┼───────────┼─────────┤
│ sync_interval   │    10     │    100    │   100   │
├─────────────────┼───────────┼───────────┼─────────┤
│ alpha           │     2     │     2     │    2    │
├─────────────────┼───────────┼───────────┼─────────┤
│ CR              │    0.9    │    0.9    │   0.9   │
├─────────────────┼───────────┼───────────┼─────────┤
│ population_size │    50     │    10     │   10    │
├─────────────────┼───────────┼───────────┼─────────┤
│ F               │    0.5    │    0.5    │   0.5   │
╰─────────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 17:32:29           tools.py->line(207)|
=================== AutoSAEAClient ==================
╭─────────────────┬───────────┬───────────┬─────────╮
│ Parameter       │  Default  │  Updates  │  Using  │
├─────────────────┼───────────┼───────────┼─────────┤
│ sync_interval   │    10     │    100    │   100   │
├─────────────────┼───────────┼───────────┼─────────┤
│ population_size │    50     │    10     │   10    │
├─────────────────┼───────────┼───────────┼─────────┤
│ CR              │    0.9    │    0.9    │   0.9   │
├─────────────────┼───────────┼───────────┼─────────┤
│ alpha           │     2     │     2     │    2    │
├─────────────────┼───────────┼───────────┼─────────┤
│ F               │    0.5    │    0.5    │   0.5   │
╰─────────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 17:44:40           tools.py->line(207)|
===================== AutoSAEAClient ====================
╭─────────────────────┬───────────┬───────────┬─────────╮
│ Parameter           │  Default  │  Updates  │  Using  │
├─────────────────────┼───────────┼───────────┼─────────┤
│ CR                  │    0.9    │    0.9    │   0.9   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ alpha               │    2.0    │     2     │   2.0   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ F                   │    0.5    │    0.5    │   0.5   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ population_size     │    50     │    10     │   10    │
├─────────────────────┼───────────┼───────────┼─────────┤
│ sync_interval       │    10     │    100    │   100   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ model_sync_interval │     -     │    100    │    -    │
╰─────────────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 17:45:33           tools.py->line(207)|
===================== AutoSAEAClient ====================
╭─────────────────────┬───────────┬───────────┬─────────╮
│ Parameter           │  Default  │  Updates  │  Using  │
├─────────────────────┼───────────┼───────────┼─────────┤
│ model_sync_interval │     -     │    100    │    -    │
├─────────────────────┼───────────┼───────────┼─────────┤
│ population_size     │    50     │    10     │   10    │
├─────────────────────┼───────────┼───────────┼─────────┤
│ alpha               │    2.0    │     2     │   2.0   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ F                   │    0.5    │    0.5    │   0.5   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ sync_interval       │    10     │    100    │   100   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ CR                  │    0.9    │    0.9    │   0.9   │
╰─────────────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 17:46:06           tools.py->line(207)|
===================== AutoSAEAClient ====================
╭─────────────────────┬───────────┬───────────┬─────────╮
│ Parameter           │  Default  │  Updates  │  Using  │
├─────────────────────┼───────────┼───────────┼─────────┤
│ alpha               │    2.0    │     2     │   2.0   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ model_sync_interval │     -     │    100    │    -    │
├─────────────────────┼───────────┼───────────┼─────────┤
│ CR                  │    0.9    │    0.9    │   0.9   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ sync_interval       │    10     │    100    │   100   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ population_size     │    50     │    10     │   10    │
├─────────────────────┼───────────┼───────────┼─────────┤
│ F                   │    0.5    │    0.5    │   0.5   │
╰─────────────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 17:46:45           tools.py->line(207)|
===================== AutoSAEAClient ====================
╭─────────────────────┬───────────┬───────────┬─────────╮
│ Parameter           │  Default  │  Updates  │  Using  │
├─────────────────────┼───────────┼───────────┼─────────┤
│ population_size     │    50     │    10     │   10    │
├─────────────────────┼───────────┼───────────┼─────────┤
│ alpha               │    2.0    │     2     │   2.0   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ F                   │    0.5    │    0.5    │   0.5   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ sync_interval       │    10     │    100    │   100   │
├─────────────────────┼───────────┼───────────┼─────────┤
│ model_sync_interval │     -     │    100    │    -    │
├─────────────────────┼───────────┼───────────┼─────────┤
│ CR                  │    0.9    │    0.9    │   0.9   │
╰─────────────────────┴───────────┴───────────┴─────────╯
