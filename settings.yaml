# settings.yaml - FMDLES 最佳参数配置 for pyfmto framework
results: out/results

launcher:
  algorithms: [IAFFBO]
  problems: [arxiv2017]
  repeat: 20
  save_res: True
  clean_tmp: True

problems:
  arxiv2017:
    dim: 10
    fe_max: 110
    fe_init: 21
    np_per_dim: [0,2,4,6]

# 算法参数配置
algorithms:
  FMDLES:
    client:
      exploration_ratio: 0.7 # 控制评估次数的分配比例
      exploration_iterations: 20
      learning_rate: 0.3
      epochs: 6
    server:
      # 最佳实验参数配置
      similarity_threshold: 0.3  # 最佳相似性阈值
      top_k_similar: 6  # 最佳top_k_similar值
      use_personalized_aggregation: 1

  # IAFFBO算法配置（CPU优化版本）
  IAFFBO:
    client:
      # 算法核心参数（匹配MATLAB默认值）
      acq_type: LCB              # 获取函数类型：LCB/UCB/EI
      phi: 0.1                   # CSO学习参数
      max_iter: 100              # CSO最大迭代次数
      privacy_noise: 0.0         # 隐私噪声比例
      flag_transfer: 1           # 迁移学习标志（匹配MATLAB）

    server:
      n_clusters: 6              # 聚类数量（匹配MATLAB）
      n_samples: 100             # x_hat采样数量
      agg_interval: 0.3          # 聚合间隔

      # GPU服务器优化
      device: cuda               # 服务器设备选择
      batch_aggregation: True    # 批量聚合优化

  # AUTOSAEA算法配置（自动模型和准则配置的代理辅助进化算法）
  AUTOSAEA:
    client:
      # 核心算法参数
      population_size: 50        # 种群大小
      alpha: 2.0                 # UCB参数，控制探索-利用平衡
      F: 0.5                     # DE变异因子
      CR: 0.9                    # DE交叉概率
      sync_interval: 10          # MAB策略同步间隔
      model_sync_interval: 20    # 模型参数同步间隔

      # 代理模型参数
      gp_alpha: 1e-6            # GP噪声水平参数
      gp_normalize_y: True      # GP是否标准化目标值
      knn_k: 1                  # KNN邻居数量
      knn_n_levels: 5           # KNN分类层数

      # 填充准则参数
      lcb_w: 2.0                # LCB权重参数
      localsearch_max_iter: 1000 # 局部搜索最大迭代数

    server:
      # 聚合策略参数
      aggregation_strategy: weighted  # MAB经验聚合策略：'average' 或 'weighted'
      min_clients_for_aggregation: 2  # 聚合所需最小客户端数量
      global_strategy_weight: 0.2     # 全局策略影响权重

      # 服务器优化参数
      reaggregation_interval: 2       # 重新聚合间隔
      strategy_update_threshold: 0.1  # 策略更新阈值

reporter:
  algorithms:
    - [FDEMD,FMTBO,IAFFBO,FMDLES]
  problems: [arxiv2017]
  np_per_dim: [2]  
